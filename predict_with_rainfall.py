import os
import pandas as pd
import numpy as np
import joblib
import datetime
from typing import Iterable, Union, List
import warnings

# --------------------------------------------------
# 1. 特征工程（只依赖日期和降雨）
# --------------------------------------------------
def _create_features_for_prediction(df: pd.DataFrame) -> pd.DataFrame:
    """
    仅基于 Date 和 Rain 列生成模型所需特征。
    与训练时 create_features_for_prediction() 保持一致。
    """
    df = df.copy()
    date_col = "Date"

    # 确保日期为 datetime
    df[date_col] = pd.to_datetime(df[date_col])

    # 时间特征
    df["month"] = df[date_col].dt.month
    df["dayofweek"] = df[date_col].dt.dayofweek
    df["dayofyear"] = df[date_col].dt.dayofyear

    # 降雨滚动/滞后
    for w in [3, 7, 14]:
        df[f"rain_roll_mean_{w}"] = df["Rain"].rolling(window=w, min_periods=1).mean()
        df[f"rain_roll_std_{w}"]  = df["Rain"].rolling(window=w, min_periods=1).std()
        df[f"rain_roll_max_{w}"]  = df["Rain"].rolling(window=w, min_periods=1).max()

    for lag in range(1, 8):
        df[f"rain_lag_{lag}"] = df["Rain"].shift(lag)

    # 添加自定义的 level 特征
    # 0-1000为level1，1000-100000为level2，100000-10000000为level3
    def get_level(rainfall):
        if rainfall < 1000:
            return 1
        elif rainfall < 100000:
            return 2
        elif rainfall < 10000000:
            return 3
        else:
            return 3  # 超过10000000也归为level3
    
    df["level"] = df["Rain"].apply(get_level)
    
    # 调试信息
    print(f"特征工程完成，生成的特征: {list(df.columns)}")
    print(f"level 特征值示例: {df['level'].head().tolist()}")
    print(f"Rain 值示例: {df['Rain'].head().tolist()}")

    df = df.fillna(0)
    return df

# --------------------------------------------------
# 2. 主预测函数
# --------------------------------------------------
def predict_spore_risk(
    date: Union[str, datetime.date, datetime.datetime, Iterable],
    rainfall: Union[float, Iterable]
) -> pd.DataFrame:
    """
    输入：
        date     : 日期  (单值或序列)
                    支持 str/date/datetime 或 list/Series
        rainfall : 降雨量 (单值或序列)

    返回：
        DataFrame 包含列 ['日期', '降雨量', '风险等级']
    """

    # 1) 统一转成 list
    if isinstance(date, (str, datetime.date, datetime.datetime, pd.Timestamp)):
        date = [date]
    if np.isscalar(rainfall):
        rainfall = [rainfall]

    # 2) 构造 30 天序列（若只给一条记录）
    if len(date) == 1:
        # 以给定日期为第 1 天，补齐后续 29 天
        start = pd.to_datetime(date[0])
        full_dates = pd.date_range(start, periods=30, freq="D")
        # 降雨：第 1 天用给定值，其余补 0（可自行改为插值或气候平均）
        full_rain = [rainfall[0]] + [0.0] * 29
    else:
        full_dates = pd.to_datetime(date)
        full_rain = list(rainfall)

    # 3) 构造输入 DataFrame
    input_df = pd.DataFrame({"Date": full_dates, "Rain": full_rain})

    # 4) 特征工程
    input_processed = _create_features_for_prediction(input_df)

    # 5) 加载模型（添加错误处理）
    model_path = os.path.join(
        os.path.dirname(__file__), "gtd_rainfall_prediction_model.joblib"
    )
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"模型文件不存在: {model_path}")
    
    try:
        # 尝试加载模型
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            pipeline = joblib.load(model_path)
    except Exception as e:
        print(f"模型加载失败: {e}")
        print("建议解决方案:")
        print("1. 降级 scikit-learn: pip install scikit-learn==1.0.2")
        print("2. 重新训练模型")
        print("3. 检查模型文件是否损坏")
        raise

    # 6) 提取特征并预测
    # 添加特征检查
    print(f"模型期望的特征数量: {len(pipeline['feature_names'])}")
    print(f"当前生成的特征数量: {len(input_processed.columns)}")
    print(f"当前生成的特征: {list(input_processed.columns)}")
    print(f"模型期望的特征: {pipeline['feature_names']}")
    
    # 检查缺失的特征
    missing_features = set(pipeline["feature_names"]) - set(input_processed.columns)
    if missing_features:
        print(f"缺失的特征: {missing_features}")
        print(f"当前所有特征: {list(input_processed.columns)}")
        raise ValueError(f"特征不匹配: 缺少特征 {missing_features}")
    
    # 检查多余的特征
    extra_features = set(input_processed.columns) - set(pipeline["feature_names"])
    if extra_features:
        print(f"多余的特征: {extra_features}")
    
    X_new = input_processed[pipeline["feature_names"]]
    print(f"输入特征形状: {X_new.shape}")
    print(f"输入特征示例:\n{X_new.head()}")
    
    base_preds = np.vstack([
        m.predict(X_new) for m in pipeline["base_models"]
    ]).T
    print(f"基础模型预测形状: {base_preds.shape}")
    print(f"基础模型预测范围: {base_preds.min():.2f} - {base_preds.max():.2f}")
    
    risk_levels = pipeline["meta_model"].predict(base_preds)
    print(f"最终预测形状: {risk_levels.shape}")
    print(f"最终预测范围: {risk_levels.min():.2f} - {risk_levels.max():.2f}")
    print(f"最终预测示例: {risk_levels[:5]}")

    # 7) 构造结果
    # 将预测的孢子数转换为 level 等级
    def get_level_from_spores(spores):
        if spores < 1000:
            return 1
        elif spores < 100000:
            return 2
        elif spores < 10000000:
            return 3
        else:
            return 3  # 超过10000000也归为level3
    
    # 将孢子数转换为 level 等级
    risk_levels_as_levels = [get_level_from_spores(spores) for spores in risk_levels]
    
    result = pd.DataFrame({
        "日期": full_dates,
        "降雨量": full_rain,
        "孢子数": risk_levels.astype(float),  # 原始预测的孢子数
        "风险等级": risk_levels_as_levels  # 转换为 level 等级
    })
    
    # 保存预测结果到CSV文件
    output_filename = f"predicted_spores_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv"
    result.to_csv(output_filename, index=False, encoding='utf-8')
    print(f"预测结果已保存到: {output_filename}")
    
    return result

