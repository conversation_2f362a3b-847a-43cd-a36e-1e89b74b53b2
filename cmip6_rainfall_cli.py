#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CMIP6降雨数据可视化工具 - 命令行版本
支持多场景、多年份、多月份、多国家的数据提取和可视化
作者: AI Assistant
日期: 2025-01-01
"""

import os
import sys
from pathlib import Path
import argparse

# 添加vertify目录到路径
vertify_dir = Path(__file__).parent / "vertify"
if str(vertify_dir) not in sys.path:
    sys.path.insert(0, str(vertify_dir))

from extract_country_data import CountryRainfallExtractor

def parse_list_input(input_str, available_items):
    """解析列表输入，支持逗号分隔、范围等"""
    if not input_str or input_str.lower() == 'all':
        return available_items
    
    result = []
    parts = input_str.split(',')
    
    for part in parts:
        part = part.strip()
        if '-' in part and part.replace('-', '').isdigit():
            # 处理范围，如 "1-3"
            start, end = map(int, part.split('-'))
            result.extend(range(start, end + 1))
        elif part.isdigit():
            # 处理单个数字
            result.append(int(part))
        else:
            # 处理字符串
            result.append(part)
    
    return list(set(result))  # 去重

def interactive_selection():
    """交互式选择参数"""
    print("="*60)
    print("CMIP6降雨数据可视化工具 - 交互式配置")
    print("="*60)
    
    # 初始化提取器
    print("正在初始化数据提取器...")
    try:
        tif_dir = vertify_dir
        shp_dir = vertify_dir / '全球国家边界_按照国家分SHP'
        extractor = CountryRainfallExtractor(str(tif_dir), str(shp_dir))
        available_countries = extractor.get_available_countries()
        print(f"✓ 初始化完成! 加载了 {len(available_countries)} 个国家")
    except Exception as e:
        print(f"✗ 初始化失败: {e}")
        return None
    
    # 1. 选择场景
    scenarios = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
    print(f"\n1. 可用场景: {scenarios}")
    print("选择方式: 1,3 (选择第1和第3个) 或 all (全部) 或 1-4 (范围)")
    while True:
        scenario_input = input("请选择场景: ").strip()
        try:
            if scenario_input.lower() == 'all':
                selected_scenarios = scenarios
            else:
                indices = parse_list_input(scenario_input, list(range(1, len(scenarios)+1)))
                selected_scenarios = [scenarios[i-1] for i in indices if 1 <= i <= len(scenarios)]
            
            if selected_scenarios:
                print(f"✓ 已选择场景: {selected_scenarios}")
                break
            else:
                print("无效选择，请重新输入")
        except Exception as e:
            print(f"输入错误: {e}")
    
    # 2. 选择年份
    years = [2025, 2026, 2027, 2028]
    print(f"\n2. 可用年份: {years}")
    print("选择方式: 2025,2027 或 all 或 2025-2027")
    while True:
        year_input = input("请选择年份: ").strip()
        try:
            if year_input.lower() == 'all':
                selected_years = years
            elif '-' in year_input:
                start, end = map(int, year_input.split('-'))
                selected_years = [y for y in range(start, end+1) if y in years]
            else:
                year_list = [int(y.strip()) for y in year_input.split(',')]
                selected_years = [y for y in year_list if y in years]
            
            if selected_years:
                print(f"✓ 已选择年份: {selected_years}")
                break
            else:
                print("无效选择，请重新输入")
        except Exception as e:
            print(f"输入错误: {e}")
    
    # 3. 选择月份
    print(f"\n3. 可用月份: 1-12")
    print("选择方式: 6,8,12 或 all 或 6-8 或 summer(6-8) 或 winter(12,1,2)")
    while True:
        month_input = input("请选择月份: ").strip()
        try:
            if month_input.lower() == 'all':
                selected_months = list(range(1, 13))
            elif month_input.lower() == 'summer':
                selected_months = [6, 7, 8]
            elif month_input.lower() == 'winter':
                selected_months = [12, 1, 2]
            elif '-' in month_input:
                start, end = map(int, month_input.split('-'))
                selected_months = list(range(start, end+1))
            else:
                selected_months = [int(m.strip()) for m in month_input.split(',')]
            
            # 验证月份范围
            selected_months = [m for m in selected_months if 1 <= m <= 12]
            
            if selected_months:
                print(f"✓ 已选择月份: {selected_months}")
                break
            else:
                print("无效选择，请重新输入")
        except Exception as e:
            print(f"输入错误: {e}")
    
    # 4. 选择国家
    print(f"\n4. 可用国家 (共{len(available_countries)}个):")
    print("常用国家: China, United States, Brazil, India, Russia, Japan")
    print("选择方式: China,Brazil 或 all 或 World (全球)")
    
    # 显示部分国家列表
    print("部分国家列表:")
    for i, country in enumerate(available_countries[:20], 1):
        print(f"  {i:2d}. {country}")
    if len(available_countries) > 20:
        print(f"  ... 还有 {len(available_countries)-20} 个国家")
    
    while True:
        country_input = input("请选择国家: ").strip()
        try:
            if country_input.lower() == 'all':
                selected_countries = available_countries
            elif country_input.lower() == 'world':
                selected_countries = ['World']
            else:
                # 支持国家名称直接输入
                country_names = [c.strip() for c in country_input.split(',')]
                selected_countries = []
                
                for name in country_names:
                    if name.isdigit():
                        # 数字索引
                        idx = int(name) - 1
                        if 0 <= idx < len(available_countries):
                            selected_countries.append(available_countries[idx])
                    else:
                        # 国家名称匹配
                        matches = [c for c in available_countries if name.lower() in c.lower()]
                        if matches:
                            selected_countries.extend(matches)
                        elif name == 'World':
                            selected_countries.append('World')
            
            if selected_countries:
                print(f"✓ 已选择国家: {selected_countries}")
                break
            else:
                print("无效选择，请重新输入")
        except Exception as e:
            print(f"输入错误: {e}")
    
    # 5. 选择颜色方案
    color_schemes = ['viridis', 'plasma', 'coolwarm', 'RdYlBu_r', 'Spectral_r', 'Blues', 'YlOrRd']
    print(f"\n5. 颜色方案: {color_schemes}")
    color_input = input("请选择颜色方案 (默认viridis): ").strip()
    color_scheme = color_input if color_input in color_schemes else 'viridis'
    print(f"✓ 已选择颜色方案: {color_scheme}")
    
    # 6. 输出目录
    output_dir = input("\n6. 输出目录 (默认extracted_data): ").strip()
    if not output_dir:
        output_dir = "extracted_data"
    print(f"✓ 输出目录: {output_dir}")
    
    # 确认配置
    total_tasks = len(selected_scenarios) * len(selected_years) * len(selected_months) * len(selected_countries)
    print(f"\n" + "="*60)
    print("配置确认:")
    print(f"  场景: {selected_scenarios}")
    print(f"  年份: {selected_years}")
    print(f"  月份: {selected_months}")
    print(f"  国家: {selected_countries}")
    print(f"  颜色: {color_scheme}")
    print(f"  输出: {output_dir}")
    print(f"  总任务数: {total_tasks}")
    print("="*60)
    
    confirm = input("确认开始处理? (y/n, 默认y): ").strip().lower()
    if confirm == 'n':
        print("已取消处理")
        return None
    
    return {
        'extractor': extractor,
        'scenarios': selected_scenarios,
        'years': selected_years,
        'months': selected_months,
        'countries': selected_countries,
        'color_scheme': color_scheme,
        'output_dir': output_dir
    }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='CMIP6降雨数据可视化工具')
    parser.add_argument('--scenarios', type=str, help='场景列表，逗号分隔 (如: ssp126,ssp245)')
    parser.add_argument('--years', type=str, help='年份列表，逗号分隔 (如: 2025,2026)')
    parser.add_argument('--months', type=str, help='月份列表，逗号分隔 (如: 6,7,8)')
    parser.add_argument('--countries', type=str, help='国家列表，逗号分隔 (如: China,Brazil)')
    parser.add_argument('--color', type=str, default='viridis', help='颜色方案')
    parser.add_argument('--output', type=str, default='extracted_data', help='输出目录')
    parser.add_argument('--interactive', action='store_true', help='交互式模式')
    
    args = parser.parse_args()
    
    if args.interactive or not any([args.scenarios, args.years, args.months, args.countries]):
        # 交互式模式
        config = interactive_selection()
        if config is None:
            return
    else:
        # 命令行参数模式
        print("使用命令行参数模式...")
        try:
            tif_dir = vertify_dir
            shp_dir = vertify_dir / '全球国家边界_按照国家分SHP'
            extractor = CountryRainfallExtractor(str(tif_dir), str(shp_dir))
            
            config = {
                'extractor': extractor,
                'scenarios': args.scenarios.split(',') if args.scenarios else ['ssp126'],
                'years': [int(y) for y in args.years.split(',')] if args.years else [2025],
                'months': [int(m) for m in args.months.split(',')] if args.months else [6],
                'countries': args.countries.split(',') if args.countries else ['World'],
                'color_scheme': args.color,
                'output_dir': args.output
            }
        except Exception as e:
            print(f"初始化失败: {e}")
            return
    
    # 执行批量处理
    print(f"\n开始批量处理...")
    try:
        results_df = config['extractor'].batch_extract_and_plot(
            countries=config['countries'],
            scenarios=config['scenarios'],
            years=config['years'],
            months=config['months'],
            output_dir=config['output_dir'],
            color_scheme=config['color_scheme']
        )
        
        print(f"\n处理完成!")
        print(f"结果汇总已保存至: {config['output_dir']}/batch_processing_summary.csv")
        
    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
