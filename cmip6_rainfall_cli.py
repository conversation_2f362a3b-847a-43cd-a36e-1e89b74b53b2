#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CMIP6降雨数据可视化工具 - 命令行版本
支持多场景、多年份、多月份、多国家的数据提取和可视化
作者: AI Assistant
日期: 2025-01-01
"""

import os
import sys
from pathlib import Path
import argparse
import numpy as np
import pandas as pd
import geopandas as gpd
import matplotlib.pyplot as plt
import matplotlib.colors as colors
from matplotlib.patches import Rectangle
import cartopy.crs as ccrs
import cartopy.feature as cfeature
from cartopy.mpl.gridliner import LONGITUDE_FORMATTER, LATITUDE_FORMATTER
import rasterio
from rasterio.features import geometry_mask
from shapely.geometry import mapping
from shapely.ops import unary_union
import calendar

class CMIP6RainfallTool:
    """CMIP6降雨数据处理工具"""

    def __init__(self):
        """初始化工具"""
        self.base_dir = Path(__file__).parent / "vertify"
        self.shp_dir = self.base_dir / "全球国家边界_按照国家分SHP"
        self.main_shp_path = self.shp_dir / '世界各国行政区划.shp'

        if not self.main_shp_path.exists():
            raise FileNotFoundError(f"主shapefile不存在: {self.main_shp_path}")

        # 加载国家边界数据
        self.world_gdf = gpd.read_file(self.main_shp_path)
        self.country_name_col = 'FENAME'

        # 场景配置
        self.scenarios = {
            "ssp126": "pr_ssp126_202501-202812",
            "ssp245": "pr_ssp245_202501-202812",
            "ssp370": "pr_ssp370_202501-202812",
            "ssp585": "pr_ssp585_202501-202812"
        }

        # 2pr文件夹（-180到180度坐标）
        self.scenarios_2pr = {
            "ssp126": "2pr_ssp126_202501-202812",
            "ssp245": "2pr_ssp245_202501-202812",
            "ssp370": "2pr_ssp370_202501-202812",
            "ssp585": "2pr_ssp585_202501-202812"
        }

        # 需要使用pr文件夹的特殊国家（0-360度坐标）
        self.special_countries = ['New Zealand', 'United States', 'United States of America', 'USA', 'US', 'America']

        # 预定义的国家显示范围（修复地图显示问题）
        self.country_extents = {
            'United States': [-130, -65, 20, 50],  # 美国本土
            'United States of America': [-130, -65, 20, 50],
            'USA': [-130, -65, 20, 50],
            'US': [-130, -65, 20, 50],
            'America': [-130, -65, 20, 50],
            'China': [70, 140, 15, 55],
            'Brazil': [-75, -30, -35, 10],
            'India': [65, 100, 5, 40],
            'Russia': [20, 180, 40, 85],
            'Canada': [-150, -50, 40, 85],
            'Australia': [110, 160, -45, -10],
            'New Zealand': [165, 180, -48, -34],
            'Japan': [125, 150, 25, 50],
            'United Kingdom': [-12, 5, 49, 62],
            'France': [-8, 10, 41, 52],
            'Germany': [5, 16, 47, 56],
            'Italy': [6, 19, 36, 48],
            'Spain': [-10, 5, 35, 45],
        }

    def get_available_countries(self):
        """获取可用国家列表"""
        return sorted(self.world_gdf[self.country_name_col].dropna().unique())

    def get_country_gdf(self, country_name):
        """获取国家的地理数据"""
        if country_name == "World":
            return None

        # 尝试直接匹配
        country_gdf = self.world_gdf[self.world_gdf[self.country_name_col].str.upper() == country_name.upper()]

        # 如果没找到，尝试模糊匹配
        if country_gdf.empty:
            for col_name in self.world_gdf[self.country_name_col].dropna():
                if country_name.lower() in col_name.lower() or col_name.lower() in country_name.lower():
                    country_gdf = self.world_gdf[self.world_gdf[self.country_name_col] == col_name]
                    break

        if country_gdf.empty:
            raise FileNotFoundError(f"在shapefile中找不到国家: '{country_name}'")

        return country_gdf

    def get_country_display_extent(self, country_name):
        """获取国家的显示范围"""
        if country_name == "World":
            return [-180, 180, -90, 90]

        # 使用预定义范围
        if country_name in self.country_extents:
            return self.country_extents[country_name]

        # 如果没有预定义，尝试计算
        try:
            country_gdf = self.get_country_gdf(country_name)
            if country_gdf is not None:
                bounds = country_gdf.total_bounds
                minx, miny, maxx, maxy = bounds

                # 添加缓冲区
                buffer = 0.1
                width = maxx - minx
                height = maxy - miny

                extent = [
                    max(minx - width * buffer, -180),
                    min(maxx + width * buffer, 180),
                    max(miny - height * buffer, -90),
                    min(maxy + height * buffer, 90)
                ]

                return extent
        except:
            pass

        # 默认全球范围
        return [-180, 180, -90, 90]

    def load_tif_data(self, scenario, year, month, country_name):
        """加载TIF数据"""
        # 确定使用哪个文件夹
        if any(special in country_name for special in self.special_countries):
            scenario_folder = self.scenarios[scenario]
            file_prefix = "pr"
        else:
            scenario_folder = self.scenarios_2pr[scenario]
            file_prefix = "2pr"

        # 构建文件路径
        tif_path = self.base_dir / scenario_folder / f"{file_prefix}_{year}{month:02d}.tif"

        if not tif_path.exists():
            raise FileNotFoundError(f"TIF文件不存在: {tif_path}")

        # 读取TIF数据
        with rasterio.open(tif_path) as src:
            data = src.read(1).astype('float32')
            transform = src.transform
            crs = src.crs

            # 转换为月总降雨量（乘以当月天数）
            days_in_month = calendar.monthrange(year, month)[1]
            data = data * days_in_month

            return data, transform, crs

    def mask_data_by_country(self, data, transform, country_gdf):
        """根据国家边界裁剪数据"""
        if country_gdf is None:
            return data  # 全球数据

        try:
            # 合并所有几何体
            geom = unary_union(country_gdf.geometry.buffer(0))

            # 创建掩膜
            mask = geometry_mask([mapping(geom)], transform=transform,
                               invert=True, out_shape=data.shape)

            # 应用掩膜
            masked_data = np.where(mask, data, np.nan)
            return masked_data

        except Exception as e:
            print(f"掩膜创建失败: {e}")
            return data

    def create_coordinate_grids(self, data, transform):
        """创建坐标网格"""
        height, width = data.shape

        # 计算每个像素的中心坐标
        cols = np.arange(width)
        rows = np.arange(height)

        # 使用rasterio的坐标转换
        lons = []
        lats = []

        for col in cols:
            x, _ = rasterio.transform.xy(transform, 0, col, offset='center')
            lons.append(x)

        for row in rows:
            _, y = rasterio.transform.xy(transform, row, 0, offset='center')
            lats.append(y)

        lon_grid, lat_grid = np.meshgrid(lons, lats)
        return lon_grid, lat_grid

    def add_scale_bar(self, ax, extent):
        """添加比例尺"""
        try:
            # 计算比例尺位置（左下角）
            x_range = extent[1] - extent[0]
            y_range = extent[3] - extent[2]

            scale_x = extent[0] + x_range * 0.05
            scale_y = extent[2] + y_range * 0.1

            # 计算比例尺长度
            scale_length = min(x_range * 0.2, 10.0)  # 最大10度
            if scale_length > 5:
                scale_length = 5
            elif scale_length > 2:
                scale_length = 2
            elif scale_length > 1:
                scale_length = 1
            else:
                scale_length = 0.5

            # 绘制比例尺
            ax.plot([scale_x, scale_x + scale_length], [scale_y, scale_y],
                   'k-', linewidth=3, transform=ccrs.PlateCarree())

            # 添加标签
            ax.text(scale_x + scale_length/2, scale_y - y_range*0.03,
                   f'{scale_length:.1f}°', ha='center', va='top',
                   transform=ccrs.PlateCarree(), fontsize=10,
                   bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))
        except Exception as e:
            print(f"比例尺添加失败: {e}")

    def add_north_arrow(self, ax, extent):
        """添加指北针"""
        try:
            # 计算指北针位置（右上角）
            x_range = extent[1] - extent[0]
            y_range = extent[3] - extent[2]

            arrow_x = extent[1] - x_range * 0.08
            arrow_y = extent[3] - y_range * 0.12

            # 绘制箭头
            ax.annotate('', xy=(arrow_x, arrow_y + y_range*0.05),
                       xytext=(arrow_x, arrow_y),
                       arrowprops=dict(arrowstyle='->', lw=2, color='black'),
                       transform=ccrs.PlateCarree())

            # 添加N标签
            ax.text(arrow_x, arrow_y + y_range*0.07, 'N',
                   ha='center', va='center', fontsize=12, fontweight='bold',
                   transform=ccrs.PlateCarree(),
                   bbox=dict(boxstyle='circle,pad=0.2', facecolor='white', alpha=0.8))
        except Exception as e:
            print(f"指北针添加失败: {e}")

    def create_visualization(self, data, lon_grid, lat_grid, country_name,
                           scenario, year, month, color_scheme='viridis'):
        """创建热图可视化"""
        # 获取显示范围
        extent = self.get_country_display_extent(country_name)

        # 创建图形
        fig = plt.figure(figsize=(12, 8))
        ax = plt.axes(projection=ccrs.PlateCarree())

        # 设置地图范围
        ax.set_extent(extent, crs=ccrs.PlateCarree())

        # 添加地图要素
        ax.add_feature(cfeature.COASTLINE, linewidth=0.5)
        ax.add_feature(cfeature.BORDERS, linewidth=0.3)
        ax.add_feature(cfeature.OCEAN, color='lightblue', alpha=0.3)
        ax.add_feature(cfeature.LAND, color='lightgray', alpha=0.3)

        # 绘制降雨数据热图
        valid_data = ~np.isnan(data)
        if np.any(valid_data):
            vmin, vmax = np.nanmin(data), np.nanmax(data)

            # 创建热图
            im = ax.contourf(lon_grid, lat_grid, data, levels=50,
                           cmap=color_scheme, transform=ccrs.PlateCarree(),
                           vmin=vmin, vmax=vmax, extend='max')

            # 添加颜色条
            cbar = plt.colorbar(im, ax=ax, shrink=0.8, pad=0.05)
            cbar.set_label('Monthly Total Precipitation (mm)', fontsize=10)

        # 添加网格线
        gl = ax.gridlines(draw_labels=True, linewidth=0.5, alpha=0.5)
        gl.top_labels = False
        gl.right_labels = False

        # 添加比例尺
        self.add_scale_bar(ax, extent)

        # 添加指北针
        self.add_north_arrow(ax, extent)

        # 设置标题
        title = f"{country_name} {scenario.upper()} {year}-{month:02d} Precipitation"
        plt.title(title, fontsize=14, pad=20)

        return fig, ax

    def save_csv_data(self, data, lon_grid, lat_grid, output_path):
        """保存CSV数据"""
        try:
            # 创建数据框
            valid_mask = ~np.isnan(data)

            if np.any(valid_mask):
                lons_flat = lon_grid[valid_mask]
                lats_flat = lat_grid[valid_mask]
                data_flat = data[valid_mask]

                df = pd.DataFrame({
                    'longitude': lons_flat,
                    'latitude': lats_flat,
                    'precipitation_mm': data_flat
                })

                # 保存CSV
                df.to_csv(output_path, index=False)
                return True
            else:
                print("没有有效数据可保存")
                return False

        except Exception as e:
            print(f"CSV保存失败: {e}")
            return False

    def process_single_task(self, scenario, year, month, country_name, output_dir, color_scheme='viridis'):
        """处理单个任务"""
        try:
            # 创建输出目录
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)

            # 加载TIF数据
            data, transform, crs = self.load_tif_data(scenario, year, month, country_name)

            # 获取国家边界
            country_gdf = self.get_country_gdf(country_name)

            # 裁剪数据
            masked_data = self.mask_data_by_country(data, transform, country_gdf)

            # 创建坐标网格
            lon_grid, lat_grid = self.create_coordinate_grids(masked_data, transform)

            # 创建可视化
            fig, ax = self.create_visualization(masked_data, lon_grid, lat_grid,
                                              country_name, scenario, year, month, color_scheme)

            # 保存图片
            img_filename = f"{country_name}_{scenario}_{year}{month:02d}.png"
            img_path = output_path / img_filename
            plt.savefig(img_path, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()

            # 保存CSV数据
            csv_filename = f"{country_name}_{scenario}_{year}{month:02d}_data.csv"
            csv_path = output_path / csv_filename
            csv_success = self.save_csv_data(masked_data, lon_grid, lat_grid, csv_path)

            return True, str(img_path), str(csv_path) if csv_success else None

        except Exception as e:
            print(f"处理失败: {e}")
            return False, None, None

def parse_list_input(input_str, available_items):
    """解析列表输入，支持逗号分隔、范围等"""
    if not input_str or input_str.lower() == 'all':
        return available_items
    
    result = []
    parts = input_str.split(',')
    
    for part in parts:
        part = part.strip()
        if '-' in part and part.replace('-', '').isdigit():
            # 处理范围，如 "1-3"
            start, end = map(int, part.split('-'))
            result.extend(range(start, end + 1))
        elif part.isdigit():
            # 处理单个数字
            result.append(int(part))
        else:
            # 处理字符串
            result.append(part)
    
    return list(set(result))  # 去重

def interactive_selection():
    """交互式选择参数"""
    print("="*60)
    print("CMIP6降雨数据可视化工具 - 交互式配置")
    print("="*60)
    
    # 初始化工具
    print("正在初始化CMIP6工具...")
    try:
        tool = CMIP6RainfallTool()
        available_countries = tool.get_available_countries()
        print(f"✓ 初始化完成! 加载了 {len(available_countries)} 个国家")
    except Exception as e:
        print(f"✗ 初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    # 1. 选择场景
    scenarios = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
    print(f"\n1. 可用场景: {scenarios}")
    print("选择方式: 1,3 (选择第1和第3个) 或 all (全部) 或 1-4 (范围)")
    while True:
        scenario_input = input("请选择场景: ").strip()
        try:
            if scenario_input.lower() == 'all':
                selected_scenarios = scenarios
            else:
                indices = parse_list_input(scenario_input, list(range(1, len(scenarios)+1)))
                selected_scenarios = [scenarios[i-1] for i in indices if 1 <= i <= len(scenarios)]
            
            if selected_scenarios:
                print(f"✓ 已选择场景: {selected_scenarios}")
                break
            else:
                print("无效选择，请重新输入")
        except Exception as e:
            print(f"输入错误: {e}")
    
    # 2. 选择年份
    years = [2025, 2026, 2027, 2028]
    print(f"\n2. 可用年份: {years}")
    print("选择方式: 2025,2027 或 all 或 2025-2027")
    while True:
        year_input = input("请选择年份: ").strip()
        try:
            if year_input.lower() == 'all':
                selected_years = years
            elif '-' in year_input:
                start, end = map(int, year_input.split('-'))
                selected_years = [y for y in range(start, end+1) if y in years]
            else:
                year_list = [int(y.strip()) for y in year_input.split(',')]
                selected_years = [y for y in year_list if y in years]
            
            if selected_years:
                print(f"✓ 已选择年份: {selected_years}")
                break
            else:
                print("无效选择，请重新输入")
        except Exception as e:
            print(f"输入错误: {e}")
    
    # 3. 选择月份
    print(f"\n3. 可用月份: 1-12")
    print("选择方式: 6,8,12 或 all 或 6-8 或 summer(6-8) 或 winter(12,1,2)")
    while True:
        month_input = input("请选择月份: ").strip()
        try:
            if month_input.lower() == 'all':
                selected_months = list(range(1, 13))
            elif month_input.lower() == 'summer':
                selected_months = [6, 7, 8]
            elif month_input.lower() == 'winter':
                selected_months = [12, 1, 2]
            elif '-' in month_input:
                start, end = map(int, month_input.split('-'))
                selected_months = list(range(start, end+1))
            else:
                selected_months = [int(m.strip()) for m in month_input.split(',')]
            
            # 验证月份范围
            selected_months = [m for m in selected_months if 1 <= m <= 12]
            
            if selected_months:
                print(f"✓ 已选择月份: {selected_months}")
                break
            else:
                print("无效选择，请重新输入")
        except Exception as e:
            print(f"输入错误: {e}")
    
    # 4. 选择国家
    display_list = ["World"] + available_countries
    print(f"\n4. 可用国家 (共{len(display_list)}个):")
    print("选择方式: 1,3,5 (选择编号) 或 all (全部) 或 1-10 (范围)")

    # 分列显示国家列表
    print("\n国家列表:")
    col_width = 35
    num_cols = 3
    for i in range(0, len(display_list), num_cols):
        line_items = []
        for j in range(num_cols):
            if i + j < len(display_list):
                item = f"{(i + j + 1):>3}. {display_list[i+j]}"
                line_items.append(f"{item:<{col_width}}")
        print("".join(line_items))

    while True:
        country_input = input("\n请选择国家编号: ").strip()
        try:
            if country_input.lower() == 'all':
                selected_countries = display_list
            else:
                # 解析编号输入
                indices = parse_list_input(country_input, list(range(1, len(display_list)+1)))
                selected_countries = []

                for idx in indices:
                    if 1 <= idx <= len(display_list):
                        selected_countries.append(display_list[idx-1])

            if selected_countries:
                print(f"✓ 已选择国家: {selected_countries}")
                break
            else:
                print("无效选择，请重新输入")
        except Exception as e:
            print(f"输入错误: {e}")
    
    # 5. 选择颜色方案
    color_schemes = ['viridis', 'plasma', 'coolwarm', 'RdYlBu_r', 'Spectral_r', 'Blues', 'YlOrRd']
    print(f"\n5. 颜色方案: {color_schemes}")
    color_input = input("请选择颜色方案 (默认viridis): ").strip()
    color_scheme = color_input if color_input in color_schemes else 'viridis'
    print(f"✓ 已选择颜色方案: {color_scheme}")
    
    # 6. 输出目录
    output_dir = input("\n6. 输出目录 (默认extracted_data): ").strip()
    if not output_dir:
        output_dir = "extracted_data"
    print(f"✓ 输出目录: {output_dir}")
    
    # 确认配置
    total_tasks = len(selected_scenarios) * len(selected_years) * len(selected_months) * len(selected_countries)
    print(f"\n" + "="*60)
    print("配置确认:")
    print(f"  场景: {selected_scenarios}")
    print(f"  年份: {selected_years}")
    print(f"  月份: {selected_months}")
    print(f"  国家: {selected_countries}")
    print(f"  颜色: {color_scheme}")
    print(f"  输出: {output_dir}")
    print(f"  总任务数: {total_tasks}")
    print("="*60)
    
    confirm = input("确认开始处理? (y/n, 默认y): ").strip().lower()
    if confirm == 'n':
        print("已取消处理")
        return None
    
    return {
        'tool': tool,
        'scenarios': selected_scenarios,
        'years': selected_years,
        'months': selected_months,
        'countries': selected_countries,
        'color_scheme': color_scheme,
        'output_dir': output_dir
    }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='CMIP6降雨数据可视化工具')
    parser.add_argument('--scenarios', type=str, help='场景列表，逗号分隔 (如: ssp126,ssp245)')
    parser.add_argument('--years', type=str, help='年份列表，逗号分隔 (如: 2025,2026)')
    parser.add_argument('--months', type=str, help='月份列表，逗号分隔 (如: 6,7,8)')
    parser.add_argument('--countries', type=str, help='国家列表，逗号分隔 (如: China,Brazil)')
    parser.add_argument('--color', type=str, default='viridis', help='颜色方案')
    parser.add_argument('--output', type=str, default='extracted_data', help='输出目录')
    parser.add_argument('--interactive', action='store_true', help='交互式模式')
    
    args = parser.parse_args()
    
    if args.interactive or not any([args.scenarios, args.years, args.months, args.countries]):
        # 交互式模式
        config = interactive_selection()
        if config is None:
            return
    else:
        # 命令行参数模式
        print("使用命令行参数模式...")
        try:
            tool = CMIP6RainfallTool()

            config = {
                'tool': tool,
                'scenarios': args.scenarios.split(',') if args.scenarios else ['ssp126'],
                'years': [int(y) for y in args.years.split(',')] if args.years else [2025],
                'months': [int(m) for m in args.months.split(',')] if args.months else [6],
                'countries': args.countries.split(',') if args.countries else ['World'],
                'color_scheme': args.color,
                'output_dir': args.output
            }
        except Exception as e:
            print(f"初始化失败: {e}")
            return
    
    # 执行批量处理
    print(f"\n开始批量处理...")
    try:
        tool = config['tool']
        total_tasks = len(config['scenarios']) * len(config['years']) * len(config['months']) * len(config['countries'])

        print(f"总任务数: {total_tasks}")

        success_count = 0
        failed_count = 0
        task_num = 0

        for scenario in config['scenarios']:
            for year in config['years']:
                for month in config['months']:
                    for country in config['countries']:
                        task_num += 1
                        print(f"\n[{task_num}/{total_tasks}] 处理: {country} {scenario} {year}-{month:02d}")

                        success, img_file, csv_file = tool.process_single_task(
                            scenario, year, month, country, config['output_dir']
                        )

                        if success:
                            success_count += 1
                            print(f"✅ 成功")
                            if img_file:
                                print(f"    图片: {img_file}")
                            if csv_file:
                                print(f"    数据: {csv_file}")
                        else:
                            failed_count += 1
                            print(f"❌ 失败")

        print(f"\n处理完成!")
        print(f"成功: {success_count} 个任务")
        print(f"失败: {failed_count} 个任务")
        print(f"输出目录: {os.path.abspath(config['output_dir'])}")

    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
