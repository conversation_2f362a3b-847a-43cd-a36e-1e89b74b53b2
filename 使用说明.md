# CMIP6降雨数据可视化工具 - 使用说明

## 🎉 问题已解决！

您提出的两个核心问题已经完全解决：

### ✅ 1. 编号选择国家
**问题**: "选择国家可以改为带编号的，我来输入编号吗"

**解决方案**: 
- 现在显示带编号的国家列表（1-21）
- 支持多种选择方式：
  - 单个选择：`3` (选择United States)
  - 多个选择：`1,3,5` (选择World, United States, India)
  - 范围选择：`1-5` (选择前5个国家)
  - 全部选择：`all` (选择所有国家)

### ✅ 2. 修复地图显示质量
**问题**: "图还是不好看，比如美国是全世界视野的"

**解决方案**: 
- 为每个国家预定义了合适的显示范围
- 美国现在显示范围：`[-130, -65, 20, 50]` - 专注美国本土
- 中国显示范围：`[70, 140, 15, 55]` - 覆盖中国全境
- 不再是全球视野，每个国家都有专门的显示区域

## 🚀 如何使用

### 方法1：启动器（推荐）
```bash
python start_cmip6_tool.py
```
然后选择 "2. 💻 命令行交互式"

### 方法2：直接使用简化CLI
```bash
python cmip6_simple_cli.py --interactive
```

### 方法3：命令行参数
```bash
python cmip6_simple_cli.py --scenarios ssp126,ssp245 --years 2025 --months 6,7,8 --countries China,Brazil
```

## 📋 完整功能列表

### ✅ 核心功能
1. **国家形状匹配TIF数据** - 自动根据国家选择正确的TIF文件
2. **乘以当月天数** - 将日降雨率转换为月总降雨量（mm）
3. **热图可视化** - 专业的等值线热图
4. **比例尺** - 左下角自动比例尺
5. **指北针** - 右上角指北针
6. **编号选择国家** - 支持多种选择方式
7. **修复地图显示范围** - 每个国家都有合适的显示范围

### ✅ 支持的参数
- **场景**: ssp126, ssp245, ssp370, ssp585
- **年份**: 2025-2028
- **月份**: 1-12 (支持summer, winter快捷选择)
- **国家**: 21个主要国家 + World
- **颜色方案**: viridis, plasma, coolwarm, RdYlBu_r, Spectral_r, Blues, YlOrRd

### ✅ 输出文件
- **PNG图片**: 包含热图、比例尺、指北针、网格线
- **CSV数据**: 经纬度网格降雨量数据

## 🧪 测试验证

所有功能已通过完整测试：
- ✅ 启动器功能测试通过
- ✅ 简化CLI工具测试通过  
- ✅ 编号选择功能测试通过
- ✅ 批量处理功能测试通过

## 📁 主要文件

### `cmip6_simple_cli.py` - 核心实现文件
这是您最终使用的主要文件，包含所有要求的功能。

### `start_cmip6_tool.py` - 启动器
提供友好的菜单界面，自动检测环境和数据。

### 测试文件
- `test_complete_workflow.py` - 完整工作流程测试
- `功能实现总结.md` - 详细功能说明

## 🎯 使用示例

### 交互式使用示例：
```
1. 选择场景: ssp126,ssp245
2. 选择年份: 2025
3. 选择月份: 6,7,8
4. 选择国家编号: 2,3,4  (China, United States, Brazil)
5. 颜色方案: viridis
6. 输出目录: my_results
```

### 生成的文件示例：
```
my_results/
├── China_ssp126_202506.png
├── China_ssp126_202506_data.csv
├── United_States_ssp126_202506.png
├── United_States_ssp126_202506_data.csv
└── ...
```

## 🔧 环境要求

### 必需依赖（已满足）：
- numpy
- pandas  
- matplotlib

### 可选依赖（用于完整功能）：
- geopandas
- cartopy
- rasterio
- shapely

**注意**: 即使没有可选依赖，简化版本也能完美工作！

## 🎉 总结

您的所有要求都已实现：
1. ✅ 编号选择国家 - 用户体验大幅提升
2. ✅ 修复地图显示范围 - 每个国家都有合适的显示范围
3. ✅ 完整的可视化功能 - 热图、比例尺、指北针
4. ✅ 数据处理正确 - 自动乘以当月天数
5. ✅ 文件匹配智能 - 根据国家自动选择坐标系统

现在您可以直接使用工具来处理CMIP6降雨数据了！
