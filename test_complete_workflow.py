#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整工作流程
"""

import sys
from pathlib import Path

def test_startup_launcher():
    """测试启动器"""
    print("="*60)
    print("测试启动器功能")
    print("="*60)
    
    try:
        from start_cmip6_tool import check_dependencies, check_data_files
        
        print("1. 检查依赖库...")
        deps_ok = check_dependencies()
        
        print("\n2. 检查数据文件...")
        data_ok = check_data_files()
        
        print(f"\n启动器检查结果: 依赖={deps_ok}, 数据={data_ok}")
        return deps_ok and data_ok
        
    except Exception as e:
        print(f"❌ 启动器测试失败: {e}")
        return False

def test_simple_cli():
    """测试简化CLI工具"""
    print("\n" + "="*60)
    print("测试简化CLI工具")
    print("="*60)
    
    try:
        from cmip6_simple_cli import SimpleCMIP6Tool
        
        print("1. 初始化工具...")
        tool = SimpleCMIP6Tool()
        
        print("2. 获取国家列表...")
        countries = tool.get_available_countries()
        print(f"   找到 {len(countries)} 个国家")
        
        print("3. 测试国家显示范围...")
        test_countries = ["China", "United States", "Brazil"]
        for country in test_countries:
            extent = tool.get_country_display_extent(country)
            print(f"   {country:15s}: {extent}")
        
        print("4. 测试数据文件检测...")
        file_exists, file_path = tool.check_tif_file("ssp126", 2025, 6, "China")
        print(f"   中国2025年6月数据: {'存在' if file_exists else '不存在'}")
        
        print("5. 测试可视化生成...")
        success, img_file, csv_file = tool.process_single_task(
            "ssp126", 2025, 6, "China", "test_output_workflow"
        )
        
        if success:
            print(f"   ✅ 成功生成: {Path(img_file).name}")
            print(f"   ✅ 数据文件: {Path(csv_file).name}")
        else:
            print("   ❌ 生成失败")
        
        return success
        
    except Exception as e:
        print(f"❌ 简化CLI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_numbered_selection():
    """测试编号选择功能"""
    print("\n" + "="*60)
    print("测试编号选择功能")
    print("="*60)
    
    try:
        from cmip6_simple_cli import parse_selection
        
        test_cases = [
            ("3", "单个选择"),
            ("1,3,5", "多个选择"),
            ("1-5", "范围选择"),
            ("1,3,7-9", "混合选择"),
            ("all", "全部选择")
        ]
        
        for input_str, description in test_cases:
            try:
                result = parse_selection(input_str, 10)
                print(f"  {description:10s} '{input_str:8s}' -> {result}")
            except Exception as e:
                print(f"  {description:10s} '{input_str:8s}' -> 错误: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 编号选择测试失败: {e}")
        return False

def test_batch_processing():
    """测试批量处理"""
    print("\n" + "="*60)
    print("测试批量处理功能")
    print("="*60)
    
    try:
        from cmip6_simple_cli import SimpleCMIP6Tool
        
        tool = SimpleCMIP6Tool()
        
        # 测试小批量处理
        scenarios = ["ssp126"]
        years = [2025]
        months = [6, 7]
        countries = ["China", "Brazil"]
        
        print(f"批量任务: {len(scenarios)} 场景 × {len(years)} 年 × {len(months)} 月 × {len(countries)} 国家")
        print(f"总任务数: {len(scenarios) * len(years) * len(months) * len(countries)}")
        
        success_count = 0
        total_count = 0
        
        for scenario in scenarios:
            for year in years:
                for month in months:
                    for country in countries:
                        total_count += 1
                        print(f"\n[{total_count}] 处理: {country} {scenario} {year}-{month:02d}")
                        
                        success, img_file, csv_file = tool.process_single_task(
                            scenario, year, month, country, "test_batch_output"
                        )
                        
                        if success:
                            success_count += 1
                            print(f"    ✅ 成功")
                        else:
                            print(f"    ❌ 失败")
        
        print(f"\n批量处理结果: {success_count}/{total_count} 成功")
        return success_count == total_count
        
    except Exception as e:
        print(f"❌ 批量处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🌧️  CMIP6降雨数据可视化工具 - 完整工作流程测试")
    
    # 运行所有测试
    tests = [
        ("启动器功能", test_startup_launcher),
        ("简化CLI工具", test_simple_cli),
        ("编号选择", test_numbered_selection),
        ("批量处理", test_batch_processing)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*80}")
        print(f"开始测试: {test_name}")
        print(f"{'='*80}")
        
        try:
            result = test_func()
            results[test_name] = result
            print(f"\n{test_name} 测试结果: {'✅ 通过' if result else '❌ 失败'}")
        except Exception as e:
            results[test_name] = False
            print(f"\n{test_name} 测试异常: {e}")
    
    # 总结
    print(f"\n{'='*80}")
    print("测试总结")
    print(f"{'='*80}")
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name:15s}: {status}")
    
    passed = sum(results.values())
    total = len(results)
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过! 工具已准备就绪!")
        print("\n您现在可以使用以下方式启动工具:")
        print("1. python start_cmip6_tool.py")
        print("2. python cmip6_simple_cli.py --interactive")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查相关功能")
    
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
