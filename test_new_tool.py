#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的CMIP6工具
"""

from cmip6_rainfall_tool import CMIP6RainfallTool
import os

def test_tool():
    """测试工具基本功能"""
    print("="*60)
    print("测试CMIP6降雨数据可视化工具")
    print("="*60)
    
    try:
        # 初始化工具
        print("1. 初始化工具...")
        tool = CMIP6RainfallTool()
        print("✅ 工具初始化成功")
        
        # 获取可用国家
        print("\n2. 获取可用国家...")
        countries = tool.get_available_countries()
        print(f"✅ 找到 {len(countries)} 个国家")
        
        # 显示前10个国家
        print("\n前10个国家:")
        for i, country in enumerate(countries[:10], 1):
            print(f"  {i:2d}. {country}")
        
        # 测试单个任务处理
        print("\n3. 测试单个任务处理...")
        test_country = "China"  # 测试中国
        test_scenario = "ssp126"
        test_year = 2025
        test_month = 6
        
        print(f"测试参数: {test_country}, {test_scenario}, {test_year}-{test_month:02d}")
        
        success, img_file, csv_file = tool.process_single_task(
            test_scenario, test_year, test_month, test_country, "test_output"
        )
        
        if success:
            print("✅ 任务处理成功")
            if img_file and os.path.exists(img_file):
                print(f"  图片文件: {img_file}")
            if csv_file and os.path.exists(csv_file):
                print(f"  数据文件: {csv_file}")
        else:
            print("❌ 任务处理失败")
        
        print("\n4. 测试地图范围计算...")
        # 测试几个特殊国家的范围计算
        test_countries = ["United States", "China", "Brazil", "New Zealand"]
        
        for country in test_countries:
            try:
                geom = tool.get_country_bounds(country)
                if geom:
                    extent = tool.calculate_display_extent(country, geom)
                    print(f"  {country}: {extent}")
                else:
                    print(f"  {country}: 全球范围")
            except Exception as e:
                print(f"  {country}: 错误 - {e}")
        
        print("\n" + "="*60)
        print("测试完成!")
        print("="*60)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_tool()
