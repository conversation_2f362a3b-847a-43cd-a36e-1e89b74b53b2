#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CMIP6降雨数据可视化工具 - 主程序
支持多场景、多年份、多月份、多国家的数据提取和可视化
作者: AI Assistant
日期: 2025-01-01
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from pathlib import Path
import threading
import queue
import pandas as pd

# 添加vertify目录到路径
vertify_dir = Path(__file__).parent / "vertify"
if str(vertify_dir) not in sys.path:
    sys.path.insert(0, str(vertify_dir))

from extract_country_data import CountryRainfallExtractor

class CMIP6RainfallGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("CMIP6降雨数据可视化工具")
        self.root.geometry("800x700")
        
        # 初始化变量
        self.extractor = None
        self.available_countries = []
        self.processing_queue = queue.Queue()
        
        # 创建界面
        self.create_widgets()
        
        # 初始化数据提取器
        self.init_extractor()
    
    def create_widgets(self):
        """创建GUI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="CMIP6降雨数据可视化工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 场景选择
        ttk.Label(main_frame, text="选择场景:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.scenario_frame = ttk.Frame(main_frame)
        self.scenario_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)
        
        self.scenario_vars = {}
        scenarios = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
        for i, scenario in enumerate(scenarios):
            var = tk.BooleanVar()
            self.scenario_vars[scenario] = var
            ttk.Checkbutton(self.scenario_frame, text=scenario.upper(), 
                           variable=var).grid(row=0, column=i, padx=5)
        
        # 年份选择
        ttk.Label(main_frame, text="选择年份:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.year_frame = ttk.Frame(main_frame)
        self.year_frame.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5)
        
        self.year_vars = {}
        years = [2025, 2026, 2027, 2028]
        for i, year in enumerate(years):
            var = tk.BooleanVar()
            self.year_vars[year] = var
            ttk.Checkbutton(self.year_frame, text=str(year), 
                           variable=var).grid(row=0, column=i, padx=5)
        
        # 月份选择
        ttk.Label(main_frame, text="选择月份:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.month_frame = ttk.Frame(main_frame)
        self.month_frame.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # 月份选择按钮
        month_button_frame = ttk.Frame(self.month_frame)
        month_button_frame.grid(row=0, column=0, sticky=tk.W)
        
        ttk.Button(month_button_frame, text="全选", 
                  command=self.select_all_months).grid(row=0, column=0, padx=2)
        ttk.Button(month_button_frame, text="全不选", 
                  command=self.deselect_all_months).grid(row=0, column=1, padx=2)
        ttk.Button(month_button_frame, text="夏季(6-8)", 
                  command=self.select_summer_months).grid(row=0, column=2, padx=2)
        ttk.Button(month_button_frame, text="冬季(12,1,2)", 
                  command=self.select_winter_months).grid(row=0, column=3, padx=2)
        
        # 月份复选框
        month_check_frame = ttk.Frame(self.month_frame)
        month_check_frame.grid(row=1, column=0, sticky=tk.W, pady=5)
        
        self.month_vars = {}
        for i in range(1, 13):
            var = tk.BooleanVar()
            self.month_vars[i] = var
            ttk.Checkbutton(month_check_frame, text=f"{i}月", 
                           variable=var).grid(row=i//7, column=i%7, padx=3)
        
        # 国家选择
        ttk.Label(main_frame, text="选择国家:").grid(row=4, column=0, sticky=tk.W, pady=5)
        country_frame = ttk.Frame(main_frame)
        country_frame.grid(row=4, column=1, sticky=(tk.W, tk.E), pady=5)
        
        # 国家选择按钮
        country_button_frame = ttk.Frame(country_frame)
        country_button_frame.grid(row=0, column=0, sticky=tk.W)
        
        ttk.Button(country_button_frame, text="全选", 
                  command=self.select_all_countries).grid(row=0, column=0, padx=2)
        ttk.Button(country_button_frame, text="全不选", 
                  command=self.deselect_all_countries).grid(row=0, column=1, padx=2)
        
        # 国家列表框
        self.country_listbox = tk.Listbox(country_frame, selectmode=tk.MULTIPLE, height=8)
        self.country_listbox.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=5)
        
        country_scrollbar = ttk.Scrollbar(country_frame, orient=tk.VERTICAL, 
                                         command=self.country_listbox.yview)
        country_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.country_listbox.config(yscrollcommand=country_scrollbar.set)
        
        # 颜色方案选择
        ttk.Label(main_frame, text="颜色方案:").grid(row=5, column=0, sticky=tk.W, pady=5)
        self.color_var = tk.StringVar(value="viridis")
        color_combo = ttk.Combobox(main_frame, textvariable=self.color_var, 
                                  values=['viridis', 'plasma', 'coolwarm', 'RdYlBu_r', 
                                         'Spectral_r', 'Blues', 'YlOrRd'])
        color_combo.grid(row=5, column=1, sticky=tk.W, pady=5)
        
        # 输出目录选择
        ttk.Label(main_frame, text="输出目录:").grid(row=6, column=0, sticky=tk.W, pady=5)
        output_frame = ttk.Frame(main_frame)
        output_frame.grid(row=6, column=1, sticky=(tk.W, tk.E), pady=5)
        
        self.output_var = tk.StringVar(value="extracted_data")
        ttk.Entry(output_frame, textvariable=self.output_var, width=40).grid(row=0, column=0)
        ttk.Button(output_frame, text="浏览", 
                  command=self.browse_output_dir).grid(row=0, column=1, padx=5)
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=7, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="开始处理", 
                  command=self.start_processing).grid(row=0, column=0, padx=10)
        ttk.Button(button_frame, text="停止处理", 
                  command=self.stop_processing).grid(row=0, column=1, padx=10)
        ttk.Button(button_frame, text="清空选择", 
                  command=self.clear_selections).grid(row=0, column=2, padx=10)
        
        # 进度条
        self.progress_var = tk.StringVar(value="就绪")
        ttk.Label(main_frame, textvariable=self.progress_var).grid(row=8, column=0, columnspan=2, pady=5)
        
        self.progress_bar = ttk.Progressbar(main_frame, mode='determinate')
        self.progress_bar.grid(row=9, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # 日志文本框
        log_frame = ttk.LabelFrame(main_frame, text="处理日志", padding="5")
        log_frame.grid(row=10, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        
        self.log_text = tk.Text(log_frame, height=10, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.log_text.config(yscrollcommand=log_scrollbar.set)
        
        # 配置网格权重
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(10, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
    
    def init_extractor(self):
        """初始化数据提取器"""
        try:
            self.log("正在初始化CMIP6数据提取器...")
            
            # 配置路径
            tif_dir = vertify_dir
            shp_dir = vertify_dir / '全球国家边界_按照国家分SHP'
            
            if not shp_dir.exists():
                raise FileNotFoundError(f"Shapefile目录不存在: {shp_dir}")
            
            self.extractor = CountryRainfallExtractor(str(tif_dir), str(shp_dir))
            self.available_countries = self.extractor.get_available_countries()
            
            # 填充国家列表（带编号）
            self.country_display_list = ["World"] + self.available_countries
            for i, country in enumerate(self.country_display_list, 1):
                display_text = f"{i:3d}. {country}"
                self.country_listbox.insert(tk.END, display_text)
            
            self.log(f"✓ 初始化完成! 加载了 {len(self.available_countries)} 个国家")
            
        except Exception as e:
            self.log(f"✗ 初始化失败: {e}")
            messagebox.showerror("初始化错误", f"无法初始化数据提取器:\n{e}")
    
    def log(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def select_all_months(self):
        """选择所有月份"""
        for var in self.month_vars.values():
            var.set(True)

    def deselect_all_months(self):
        """取消选择所有月份"""
        for var in self.month_vars.values():
            var.set(False)

    def select_summer_months(self):
        """选择夏季月份(6-8月)"""
        self.deselect_all_months()
        for month in [6, 7, 8]:
            self.month_vars[month].set(True)

    def select_winter_months(self):
        """选择冬季月份(12,1,2月)"""
        self.deselect_all_months()
        for month in [12, 1, 2]:
            self.month_vars[month].set(True)

    def select_all_countries(self):
        """选择所有国家"""
        self.country_listbox.select_set(0, tk.END)

    def deselect_all_countries(self):
        """取消选择所有国家"""
        self.country_listbox.selection_clear(0, tk.END)

    def browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(initialdir=self.output_var.get())
        if directory:
            self.output_var.set(directory)

    def clear_selections(self):
        """清空所有选择"""
        # 清空场景选择
        for var in self.scenario_vars.values():
            var.set(False)

        # 清空年份选择
        for var in self.year_vars.values():
            var.set(False)

        # 清空月份选择
        self.deselect_all_months()

        # 清空国家选择
        self.deselect_all_countries()

        self.log("已清空所有选择")

    def get_selected_values(self):
        """获取用户选择的值"""
        # 获取选中的场景
        scenarios = [scenario for scenario, var in self.scenario_vars.items() if var.get()]

        # 获取选中的年份
        years = [year for year, var in self.year_vars.items() if var.get()]

        # 获取选中的月份
        months = [month for month, var in self.month_vars.items() if var.get()]

        # 获取选中的国家
        selected_indices = self.country_listbox.curselection()
        countries = []
        for i in selected_indices:
            countries.append(self.country_display_list[i])

        return scenarios, years, months, countries

    def validate_selections(self, scenarios, years, months, countries):
        """验证用户选择"""
        if not scenarios:
            messagebox.showerror("选择错误", "请至少选择一个场景!")
            return False

        if not years:
            messagebox.showerror("选择错误", "请至少选择一个年份!")
            return False

        if not months:
            messagebox.showerror("选择错误", "请至少选择一个月份!")
            return False

        if not countries:
            messagebox.showerror("选择错误", "请至少选择一个国家!")
            return False

        return True

    def start_processing(self):
        """开始处理数据"""
        if self.extractor is None:
            messagebox.showerror("错误", "数据提取器未初始化!")
            return

        # 获取用户选择
        scenarios, years, months, countries = self.get_selected_values()

        # 验证选择
        if not self.validate_selections(scenarios, years, months, countries):
            return

        # 计算总任务数
        total_tasks = len(scenarios) * len(years) * len(months) * len(countries)

        # 确认处理
        result = messagebox.askyesno(
            "确认处理",
            f"将处理 {total_tasks} 个任务:\n"
            f"场景: {len(scenarios)} 个\n"
            f"年份: {len(years)} 个\n"
            f"月份: {len(months)} 个\n"
            f"国家: {len(countries)} 个\n\n"
            f"确认开始处理吗?"
        )

        if not result:
            return

        # 清空日志
        self.log_text.delete(1.0, tk.END)

        # 设置进度条
        self.progress_bar['maximum'] = total_tasks
        self.progress_bar['value'] = 0

        # 在新线程中执行处理
        self.processing_thread = threading.Thread(
            target=self.process_data,
            args=(scenarios, years, months, countries),
            daemon=True
        )
        self.processing_thread.start()

        # 启动进度更新
        self.root.after(100, self.update_progress)

    def process_data(self, scenarios, years, months, countries):
        """在后台线程中处理数据"""
        try:
            self.processing_queue.put(("log", f"开始批量处理 {len(scenarios) * len(years) * len(months) * len(countries)} 个任务..."))

            # 使用批量处理功能
            results_df = self.extractor.batch_extract_and_plot(
                countries=countries,
                scenarios=scenarios,
                years=years,
                months=months,
                output_dir=self.output_var.get(),
                color_scheme=self.color_var.get()
            )

            # 统计结果
            success_count = len(results_df[results_df['status'] == 'success'])
            failed_count = len(results_df[results_df['status'] != 'success'])

            self.processing_queue.put(("log", f"批量处理完成!"))
            self.processing_queue.put(("log", f"✓ 成功: {success_count} 个任务"))
            self.processing_queue.put(("log", f"✗ 失败: {failed_count} 个任务"))
            self.processing_queue.put(("log", f"成功率: {success_count/(success_count+failed_count)*100:.1f}%"))

            # 显示输出位置
            self.processing_queue.put(("log", f"输出文件位置: {self.output_var.get()}/"))

            self.processing_queue.put(("complete", None))

        except Exception as e:
            self.processing_queue.put(("error", f"处理过程中发生错误: {e}"))

    def update_progress(self):
        """更新进度显示"""
        try:
            while True:
                msg_type, msg_data = self.processing_queue.get_nowait()

                if msg_type == "log":
                    self.log(msg_data)
                elif msg_type == "progress":
                    self.progress_bar['value'] = msg_data
                    self.progress_var.set(f"进度: {msg_data}/{self.progress_bar['maximum']}")
                elif msg_type == "complete":
                    self.progress_var.set("处理完成!")
                    messagebox.showinfo("完成", "数据处理完成!")
                    return
                elif msg_type == "error":
                    self.progress_var.set("处理出错!")
                    messagebox.showerror("错误", msg_data)
                    return

        except queue.Empty:
            pass

        # 继续检查队列
        self.root.after(100, self.update_progress)

    def stop_processing(self):
        """停止处理(暂时不实现具体停止逻辑)"""
        messagebox.showinfo("提示", "停止功能暂未实现，请等待当前任务完成")

def main():
    """主函数"""
    root = tk.Tk()
    app = CMIP6RainfallGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
