import os
import glob
import numpy as np
import pandas as pd
import geopandas as gpd
from shapely.geometry import Point, box, Polygon, MultiPolygon
from shapely.ops import transform as shapely_transform, unary_union
import pathlib
import cartopy.crs as ccrs
import cartopy.feature as cfeature
import matplotlib.pyplot as plt
import matplotlib.colors as colors
from matplotlib.patches import PathPatch, Rectangle
from matplotlib.path import Path
import warnings
import calendar

warnings.filterwarnings('ignore')

try:
    import rasterio
    from rasterio.features import rasterize
    RASTERIO_AVAILABLE = True
except ImportError:
    print("警告: rasterio 库不可用，部分功能将受限。")
    RASTERIO_AVAILABLE = False

def shift_geom_to_180(geom):
    """将几何体的经度从 [0, 360] 范围转换到 [-180, 180] 范围。"""
    def transform_coords(x, y, z=None):
        return tuple(xi - 360 if xi > 180 else xi for xi in x), y
    return shapely_transform(transform_coords, geom)

def clip_extent(extent, lon_min=-180, lon_max=180):
    """裁剪extent到合理的经度范围"""
    minx, maxx, miny, maxy = extent
    minx = max(lon_min, min(minx, lon_max))
    maxx = max(lon_min, min(maxx, lon_max))
    return [minx, maxx, miny, maxy]

class CountryRainfallExtractor:
    def __init__(self, tif_dir, shp_dir):
        self.tif_dir = pathlib.Path(tif_dir)
        self.shp_dir = pathlib.Path(shp_dir)
        self.main_shp_path = self.shp_dir / '世界各国行政区划.shp'
        if not self.main_shp_path.exists():
            raise FileNotFoundError(f"主shapefile不存在: {self.main_shp_path}。")
        self.world_gdf = gpd.read_file(self.main_shp_path)
        self.country_name_col = 'FENAME'
        if self.country_name_col not in self.world_gdf.columns:
            raise ValueError(f"指定的国家名称列 '{self.country_name_col}' 在shapefile中不存在。")
        self.scenarios = {
            "ssp126": "pr_ssp126_202501-202812", "ssp245": "pr_ssp245_202501-202812",
            "ssp370": "pr_ssp370_202501-202812", "ssp585": "pr_ssp585_202501-202812"
        }
        # 为其他国家使用的2pr文件夹
        self.scenarios_2pr = {
            "ssp126": "2pr_ssp126_202501-202812", "ssp245": "2pr_ssp245_202501-202812",
            "ssp370": "2pr_ssp370_202501-202812", "ssp585": "2pr_ssp585_202501-202812"
        }
        # 需要特殊处理的国家（使用原始文件夹）
        self.special_countries = ['New Zealand', 'United States', 'United States of America', 'USA', 'US', 'America']
        self.target_years = [2025, 2026, 2027, 2028]
        self.months = list(range(1, 13))

    def get_days_in_month(self, year, month):
        return calendar.monthrange(year, month)[1]

    def get_available_countries(self):
        return sorted(self.world_gdf[self.country_name_col].dropna().unique())

    def get_country_gdf(self, country_name: str):
        # 创建国家名称映射
        country_mapping = {
            'Federative Republic of Brazil': 'Brazil',
            'Republic of Chile': 'Chile',
            'United States of America': 'United States',
            'New Zealand': 'New Zealand',
            'Brazil': 'Brazil',
            'Chile': 'Chile',
            'United States': 'United States',
            # 添加更多可能的名称
            'BRAZIL': 'Brazil',
            'CHILE': 'Chile',
            'USA': 'United States',
            'US': 'United States',
            'America': 'United States'
        }
        
        # 尝试直接匹配
        country_gdf = self.world_gdf[self.world_gdf[self.country_name_col].str.upper() == country_name.upper()]
        
        # 如果没找到，尝试映射
        if country_gdf.empty and country_name in country_mapping:
            mapped_name = country_mapping[country_name]
            country_gdf = self.world_gdf[self.world_gdf[self.country_name_col].str.upper() == mapped_name.upper()]
        
        # 如果还是没找到，尝试模糊匹配
        if country_gdf.empty:
            # 移除 "Republic of", "Federative Republic of" 等前缀
            clean_name = country_name.replace('Federative Republic of ', '').replace('Republic of ', '').replace('United States of ', '')
            country_gdf = self.world_gdf[self.world_gdf[self.country_name_col].str.upper().str.contains(clean_name.upper(), na=False)]
        
        if country_gdf.empty:
            # 打印可用的国家名称以便调试
            available_countries = self.world_gdf[self.country_name_col].dropna().unique()
            print(f"查找国家: '{country_name}'")
            print(f"可用的国家名称: {available_countries[:20]}...")  # 显示前20个
            
            # 尝试查找包含关键词的国家
            if 'Brazil' in country_name or 'brazil' in country_name.lower():
                brazil_countries = [c for c in available_countries if 'brazil' in c.lower()]
                print(f"包含Brazil的国家: {brazil_countries}")
            if 'Chile' in country_name or 'chile' in country_name.lower():
                chile_countries = [c for c in available_countries if 'chile' in c.lower()]
                print(f"包含Chile的国家: {chile_countries}")
            
            raise FileNotFoundError(f"在 shapefile 的 '{self.country_name_col}' 列中找不到国家: '{country_name}'。")
        return country_gdf

    def _create_mask_from_geom(self, geom, transform, shape):
        return rasterize(shapes=[geom], out_shape=shape, transform=transform, fill=0, default_value=1, dtype='uint8', all_touched=True).astype(bool)

    def _geoseries_to_path(self, geoseries):
        all_verts, all_codes = [], []
        for geom in geoseries:
            if geom.is_empty: continue
            polygons = [geom] if geom.geom_type == 'Polygon' else list(geom.geoms)
            for poly in polygons:
                if poly.is_empty: continue
                for part in [poly.exterior] + list(poly.interiors):
                    coords = np.asarray(part.coords)
                    all_verts.extend(coords)
                    codes = np.full(len(coords), Path.LINETO, dtype=Path.code_type); codes[0] = Path.MOVETO
                    all_codes.extend(codes)
        return Path(np.empty((0, 2))) if not all_verts else Path(all_verts, all_codes)

    def _get_tight_country_extent(self, data, transform, geom, country_name):
        """获取国家数据的紧凑边界，减少空白区域"""
        # 创建掩膜
        mask = self._create_mask_from_geom(geom, transform, data.shape)
        country_data = np.where(mask, data, np.nan)
        
        # 找到有效数据的像素位置
        valid_rows, valid_cols = np.where(~np.isnan(country_data))
        
        if len(valid_rows) == 0:
            return None  # 没有有效数据
        
        # 计算有效数据的边界，添加更智能的边界处理
        min_row, max_row = valid_rows.min(), valid_rows.max()
        min_col, max_col = valid_cols.min(), valid_cols.max()
        
        # 为了避免过于紧凑，添加一定的缓冲
        buffer_rows = max(1, int((max_row - min_row) * 0.05))  # 5%缓冲
        buffer_cols = max(1, int((max_col - min_col) * 0.05))  # 5%缓冲
        
        min_row = max(0, min_row - buffer_rows)
        max_row = min(data.shape[0] - 1, max_row + buffer_rows)
        min_col = max(0, min_col - buffer_cols)
        max_col = min(data.shape[1] - 1, max_col + buffer_cols)
        
        # 转换为地理坐标
        height, width = data.shape
        cols = np.arange(width)
        rows = np.arange(height)
        lons_1d = rasterio.transform.xy(transform, 0, cols, offset='center')[0]
        lats_1d = rasterio.transform.xy(transform, rows, 0, offset='center')[1]
        
        minx = lons_1d[min_col]
        maxx = lons_1d[max_col]
        miny = lats_1d[max_row]  # 注意：纬度是反向的
        maxy = lats_1d[min_row]
        
        print(f"DEBUG: {country_name} 数据范围: lon=[{minx:.2f}, {maxx:.2f}], lat=[{miny:.2f}, {maxy:.2f}]")
        
        return minx, miny, maxx, maxy

    def _calculate_optimal_extent(self, country_name, geom, data_extent):
        """根据国家特点计算最优显示范围"""
        if data_extent is None:
            # 使用几何体边界作为备用方案
            bounds = geom.bounds
            return bounds[0], bounds[1], bounds[2], bounds[3]
        
        minx, miny, maxx, maxy = data_extent
        
        # 特殊国家的优化处理
        if country_name in ['New Zealand']:
            # 新西兰：强制使用紧凑的新西兰范围，避免显示过多无关区域
            print(f"DEBUG: 新西兰特殊处理 - 强制限制范围")
            # 新西兰：165到180经度，-55到-30纬度
            minx, maxx = 165, 180
            miny, maxy = -55, -30
            
            # 最小缓冲，确保紧凑显示
            lon_buffer = 0.3  # 固定小缓冲
            lat_buffer = 0.3
            extent = [minx - lon_buffer, maxx + lon_buffer, miny - lat_buffer, maxy + lat_buffer]
            extent = clip_extent(extent)
            print(f"DEBUG: 新西兰紧凑范围: {extent}")
            
        elif country_name in ['United States', 'United States of America', 'USA', 'US', 'America']:
            # 美国：强制限制到紧凑的美国范围，避免显示全球
            print(f"DEBUG: 美国特殊处理 - 强制限制范围")
            # 美国本土：-125到-60经度，25到50纬度
            # 阿拉斯加：-170到-125经度，50到75纬度
            minx, maxx = -170, -60
            miny, maxy = 25, 75
            
            # 适中缓冲
            lon_buffer = 1.0
            lat_buffer = 1.0
            extent = [minx - lon_buffer, maxx + lon_buffer, miny - lat_buffer, maxy + lat_buffer]
            extent = clip_extent(extent)
            print(f"DEBUG: 美国紧凑范围: {extent}")
            
        elif country_name in ['Chile', 'Republic of Chile']:
            # 智利：狭长国家，紧凑显示
            span_lon = maxx - minx
            span_lat = maxy - miny
            center_lon = (maxx + minx) / 2
            center_lat = (maxy + miny) / 2
            
            # 智利通常是纵向的，确保有足够的经度显示
            min_lon_span = span_lat * 0.25  # 减少最小经度跨度
            if span_lon < min_lon_span:
                span_lon = min_lon_span
            
            lon_buffer = span_lon * 0.08  # 减少缓冲
            lat_buffer = span_lat * 0.03
            
            extent = [center_lon - span_lon/2 - lon_buffer, center_lon + span_lon/2 + lon_buffer, 
                     center_lat - span_lat/2 - lat_buffer, center_lat + span_lat/2 + lat_buffer]
            extent = clip_extent(extent)
            print(f"DEBUG: 智利紧凑范围: {extent}")
            
        elif country_name in ['Brazil', 'Federative Republic of Brazil']:
            # 巴西：大国，减少缓冲
            lon_buffer = (maxx - minx) * 0.05  # 减少缓冲
            lat_buffer = (maxy - miny) * 0.05
            extent = [minx - lon_buffer, maxx + lon_buffer, miny - lat_buffer, maxy + lat_buffer]
            extent = clip_extent(extent)
            print(f"DEBUG: 巴西紧凑范围: {extent}")
            
        else:
            # 其他国家：更紧凑的标准处理
            span_lon = maxx - minx
            span_lat = maxy - miny
            
            # 自适应缓冲：小国家用相对缓冲，大国家用绝对缓冲
            if span_lon < 10 and span_lat < 10:  # 小国家
                lon_buffer = max(0.3, span_lon * 0.1)  # 减少缓冲
                lat_buffer = max(0.3, span_lat * 0.1)
            else:  # 大国家
                lon_buffer = span_lon * 0.05  # 减少缓冲
                lat_buffer = span_lat * 0.05
            
            extent = [minx - lon_buffer, maxx + lon_buffer, miny - lat_buffer, maxy + lat_buffer]
            extent = clip_extent(extent)
            print(f"DEBUG: {country_name} 紧凑范围: {extent}")
        
        return extent

    def extract_data(self, scenario, year, month, country_name=None, output_dir="extracted_data"):
        # 根据国家选择不同的文件夹
        if country_name and country_name in self.special_countries:
            # 新西兰和美国使用原始文件夹
            tif_path = self.tif_dir / self.scenarios[scenario] / f"pr_{year}{month:02d}.tif"
        else:
            # 其他国家使用2pr文件夹
            tif_path = self.tif_dir / self.scenarios_2pr[scenario] / f"2pr_{year}{month:02d}.tif"
        
        if not tif_path.exists(): return None, None, f"找不到TIF文件: {tif_path}"
        try:
            with rasterio.open(tif_path) as src:
                data = src.read(1).astype('float32')
                transform = src.transform
                days_in_month = self.get_days_in_month(year, month)
                print(f"INFO: 通过乘以 {days_in_month} 天将 {year}-{month:02d} 的日数据转换为月度数据。")
                data *= days_in_month
                height, width = data.shape
                cols, rows = np.arange(width), np.arange(height)
                lons_1d = rasterio.transform.xy(transform, 0, cols, offset='center')[0]
                lats_1d = rasterio.transform.xy(transform, rows, 0, offset='center')[1]
                lons, lats = np.meshgrid(lons_1d, lats_1d)

                df_data = {}
                if country_name and country_name != "World":
                    country_gdf = self.get_country_gdf(country_name)
                    country_geometry = unary_union(country_gdf.geometry.buffer(0))
                    # 创建掩膜（使用原始360度数据）
                    mask = self._create_mask_from_geom(country_geometry, transform, data.shape)
                    masked_data = np.where(mask, data, np.nan)
                    valid_indices = ~np.isnan(masked_data)
                    if not np.any(valid_indices): return None, None, f"在国家 '{country_name}' 内没有找到有效数据"
                    
                    # TIF数据已经是[-180, 180]范围，直接使用
                    df_data = {'Date': f"{year}-{month:02d}", 'lon': lons[valid_indices], 'lat': lats[valid_indices], 'Rain': masked_data[valid_indices], 'country': country_name}
                else:
                    # TIF数据已经是[-180, 180]范围，直接使用
                    df_data = {'Date': f"{year}-{month:02d}", 'lon': lons.flatten(), 'lat': lats.flatten(), 'Rain': data.flatten(), 'country': 'World'}
                
                df = pd.DataFrame(df_data)
                os.makedirs(output_dir, exist_ok=True)
                csv_file = os.path.join(output_dir, f"{scenario}_{year}_{month:02d}_{country_name or 'World'}_grid.csv")
                df.to_csv(csv_file, index=False)
                return df, csv_file, None
        except Exception as e:
            return None, None, f"提取数据时出错: {e}"

    def plot_heatmap_precise(self, df, scenario, year, month, country, out_dir="extracted_data", color_scheme='viridis'):
        out_dir_path = pathlib.Path(out_dir); out_dir_path.mkdir(parents=True, exist_ok=True)
        
        # 根据国家选择不同的文件夹
        if country and country in self.special_countries:
            # 新西兰和美国使用原始文件夹
            tif_path = self.tif_dir / self.scenarios[scenario] / f"pr_{year}{month:02d}.tif"
        else:
            # 其他国家使用2pr文件夹
            tif_path = self.tif_dir / self.scenarios_2pr[scenario] / f"2pr_{year}{month:02d}.tif"
        
        if not tif_path.exists(): return None

        try:
            with rasterio.open(tif_path) as src:
                data = src.read(1).astype('float32') * self.get_days_in_month(year, month)
                transform = src.transform
                height, width = data.shape
                cols, rows = np.arange(width), np.arange(height)
                lons_1d = rasterio.transform.xy(transform, 0, cols, offset='center')[0]
                lats_1d = rasterio.transform.xy(transform, rows, 0, offset='center')[1]
                lon_grid, lat_grid = np.meshgrid(lons_1d, lats_1d)
                
                # 根据国家设置合适的中央经线
                if country in ['United States', 'United States of America', 'USA', 'US', 'America']:
                    central_longitude = -95.0  # 美国中央经线
                elif country in ['New Zealand']:
                    central_longitude = 170.0  # 新西兰中央经线
                elif country in ['China', 'Peoples Republic of China']:
                    central_longitude = 105.0  # 中国中央经线
                else:
                    central_longitude = 0.0    # 其他国家使用默认值

                geom, country_gdf, vmin, vmax = None, None, None, None
                
                # 检查数据坐标范围
                print(f"DEBUG: 数据坐标范围 - 经度: [{lon_grid.min():.2f}, {lon_grid.max():.2f}], 纬度: [{lat_grid.min():.2f}, {lat_grid.max():.2f}]")
                
                # 处理坐标范围问题
                if lon_grid.min() < 0 and lon_grid.max() > 180:
                    # 数据跨越0度经线，需要特殊处理
                    print(f"DEBUG: 检测到跨越0度经线的数据，进行坐标转换")
                    # 将大于180度的经度转换为负值
                    plot_lon = lon_grid.copy()
                    plot_lon[plot_lon > 180] = plot_lon[plot_lon > 180] - 360
                    plot_data = data.copy()
                else:
                    # 使用原始数据坐标系统
                    plot_lon = lon_grid.copy()
                    plot_data = data.copy()
                
                if country and country != 'World':
                    country_gdf = self.get_country_gdf(country)
                    geom = unary_union(country_gdf.geometry.buffer(0))
                    if geom.is_empty: return None
                    
                    # 创建掩膜
                    mask = self._create_mask_from_geom(geom, transform, data.shape)
                    country_data_only = np.where(mask, data, np.nan)
                    vmin, vmax = np.nanmin(country_data_only), np.nanmax(country_data_only)
                    
                    # 获取优化的显示范围
                    data_extent = self._get_tight_country_extent(data, transform, geom, country)
                    extent = self._calculate_optimal_extent(country, geom, data_extent)
                    
                    # 强制应用特殊国家的范围限制
                    if country in ['New Zealand']:
                        print(f"DEBUG: 强制应用新西兰紧凑范围")
                        extent = [164.7, 180.3, -55.3, -29.7]  # 新西兰紧凑范围
                    elif country in ['United States', 'United States of America', 'USA', 'US', 'America']:
                        print(f"DEBUG: 强制应用美国紧凑范围")
                        extent = [-171, -59, 24, 76]  # 美国紧凑范围
                    elif country in ['Chile', 'Republic of Chile']:
                        print(f"DEBUG: 智利竖版格式 - 使用完整国土范围")
                        # 智利：狭长国家，使用竖版显示完整国土
                        bounds = geom.bounds
                        # 使用完整的智利边界，适当增加缓冲
                        lon_buffer = (bounds[2] - bounds[0]) * 0.1
                        lat_buffer = (bounds[3] - bounds[1]) * 0.02
                        extent = [bounds[0] - lon_buffer, bounds[2] + lon_buffer,
                                bounds[1] - lat_buffer, bounds[3] + lat_buffer]
                        print(f"DEBUG: 智利竖版范围: {extent}")
                    
                else: 
                    vmin, vmax = np.nanmin(data), np.nanmax(data)
                    extent = None

                # 统一使用标准布局 - 参考意大利地图的完美布局
                # 所有国家都使用相同的图片尺寸，确保一致性
                fig = plt.figure(figsize=(12, 8))
                fig.patch.set_facecolor('white')
                
                ax = plt.axes(projection=ccrs.PlateCarree(central_longitude=central_longitude))
                
                # 统一的地图范围设置 - 确保完美居中布局
                if country and country != 'World':
                    # 获取几何体边界
                    bounds = geom.bounds

                    # 特殊国家的优化范围
                    if country in ['New Zealand']:
                        # 新西兰：从160°E开始，紧凑显示
                        ax.set_extent([160, 180, -50, -30], crs=ccrs.PlateCarree())
                        print(f"DEBUG: 新西兰范围: [160, 180, -50, -30]")
                    elif country in ['United States', 'United States of America', 'USA', 'US', 'America']:
                        # 美国：严格限制为本土48州
                        ax.set_extent([-125, -66, 20, 50], crs=ccrs.PlateCarree())
                        print(f"DEBUG: 美国本土范围: [-125, -66, 20, 50]")
                    else:
                        # 其他国家：计算完美居中的范围
                        center_lon = (bounds[0] + bounds[2]) / 2
                        center_lat = (bounds[1] + bounds[3]) / 2

                        # 计算合适的缓冲区，确保地图居中且不会被裁切
                        lon_span = bounds[2] - bounds[0]
                        lat_span = bounds[3] - bounds[1]

                        # 根据图片比例(12:8 = 1.5:1)调整范围
                        aspect_ratio = 1.5
                        if lon_span / lat_span > aspect_ratio:
                            # 宽度较大，以宽度为准
                            buffer_lon = lon_span * 0.1
                            buffer_lat = (lon_span / aspect_ratio - lat_span) / 2 + lat_span * 0.1
                        else:
                            # 高度较大，以高度为准
                            buffer_lat = lat_span * 0.1
                            buffer_lon = (lat_span * aspect_ratio - lon_span) / 2 + lon_span * 0.1

                        extent = [
                            center_lon - lon_span/2 - buffer_lon,
                            center_lon + lon_span/2 + buffer_lon,
                            center_lat - lat_span/2 - buffer_lat,
                            center_lat + lat_span/2 + buffer_lat
                        ]

                        ax.set_extent(extent, crs=ccrs.PlateCarree())
                        print(f"DEBUG: {country} 居中范围: {extent}")
                else:
                    # 世界地图
                    ax.set_global()

                # 添加海洋背景 - 使用专业的浅蓝色
                ax.add_feature(cfeature.OCEAN, facecolor='#d6ebf5', zorder=0)
                ax.add_feature(cfeature.LAKES, facecolor='#d6ebf5', zorder=1)

                # 设置完美的网格线 - 参考意大利地图的布局
                gl = ax.gridlines(draw_labels=True, linewidth=0.6, color='#888888', alpha=0.6, linestyle='-')
                gl.top_labels = True
                gl.left_labels = True
                gl.right_labels = True
                gl.bottom_labels = True
                gl.xlabel_style = {'size': 9, 'color': 'black', 'weight': 'normal'}
                gl.ylabel_style = {'size': 9, 'color': 'black', 'weight': 'normal'}

                # 智能网格间隔设置 - 确保网格线美观且实用
                extent = ax.get_extent()
                lon_range = extent[1] - extent[0]
                lat_range = extent[3] - extent[2]

                # 根据地图范围智能调整网格间隔
                if lon_range > 50:  # 大范围地图（如美国、中国）
                    lon_step = 10
                    lat_step = 10
                elif lon_range > 20:  # 中等范围地图（如法国、意大利）
                    lon_step = 5
                    lat_step = 5
                elif lon_range > 10:  # 小范围地图
                    lon_step = 2
                    lat_step = 2
                else:  # 极小范围地图
                    lon_step = 1
                    lat_step = 1

                # 设置网格线位置
                lon_start = int(extent[0] // lon_step) * lon_step
                lon_end = int(extent[1] // lon_step + 1) * lon_step
                lat_start = int(extent[2] // lat_step) * lat_step
                lat_end = int(extent[3] // lat_step + 1) * lat_step

                gl.xlocator = plt.FixedLocator(range(lon_start, lon_end + lon_step, lon_step))
                gl.ylocator = plt.FixedLocator(range(lat_start, lat_end + lat_step, lat_step))
                
                # 智能网格设置
                if extent is not None:
                    extent_width = extent[1] - extent[0]
                    extent_height = extent[3] - extent[2]
                    
                    # 根据地图大小动态调整网格密度
                    if extent_width <= 5 and extent_height <= 5:
                        # 非常小的区域：密集网格
                        lon_step = 1
                        lat_step = 1
                    elif extent_width <= 20 and extent_height <= 15:
                        # 小区域：中等密度
                        lon_step = 2
                        lat_step = 2
                    elif extent_width <= 50 and extent_height <= 30:
                        # 中等区域：稀疏网格
                        lon_step = 5
                        lat_step = 5
                    else:
                        # 大区域：很稀疏网格
                        lon_step = 10
                        lat_step = 10
                    
                    # 生成网格位置
                    lon_start = int(extent[0] // lon_step) * lon_step
                    lon_end = int(extent[1] // lon_step + 1) * lon_step
                    lat_start = int(extent[2] // lat_step) * lat_step
                    lat_end = int(extent[3] // lat_step + 1) * lat_step
                    
                    lon_ticks = list(range(lon_start, lon_end + 1, lon_step))
                    lat_ticks = list(range(lat_start, lat_end + 1, lat_step))
                    
                    gl.xlocator = plt.FixedLocator(lon_ticks)
                    gl.ylocator = plt.FixedLocator(lat_ticks)
                else:
                    # 世界地图：标准网格
                    gl.xlocator = plt.FixedLocator([-180, -120, -60, 0, 60, 120, 180])
                    gl.ylocator = plt.FixedLocator([-90, -60, -30, 0, 30, 60, 90])

                # 处理颜色范围
                if np.isnan(vmin) or vmin == vmax: 
                    vmin = max(0, vmax - 1 if vmax > 0 else 0)
                
                # 动态调整颜色范围
                if country in ['South Africa', 'Republic of South Africa']:
                    if vmax > 100: vmax = 100
                    if vmin < 0: vmin = 0
                elif country in ['China', 'Peoples Republic of China']:
                    if vmax > 500: vmax = 500
                    if vmin < 0: vmin = 0
                
                # 创建颜色标准化 - 使用线性标准化获得更好的视觉效果
                norm = colors.Normalize(vmin=vmin, vmax=vmax)

                # 绘制等值线图 - 使用更多层级获得平滑效果
                im = ax.contourf(plot_lon, lat_grid, plot_data, levels=200, cmap=color_scheme,
                                transform=ccrs.PlateCarree(), norm=norm, zorder=2, extend='max')
                
                if country and country != 'World':
                    # 添加其他国家的灰色轮廓作为背景参考 - 专业样式
                    other_countries_gdf = self.world_gdf[self.world_gdf[self.country_name_col].str.upper() != country.upper()]
                    ax.add_geometries(other_countries_gdf.geometry, crs=ccrs.PlateCarree(),
                                    facecolor='#f0f0f0', edgecolor='#999999', linewidth=0.4, zorder=1, alpha=0.7)
                    
                    # 裁剪数据只显示在国家边界内
                    for collection in im.collections:
                        collection.set_clip_path(PathPatch(self._geoseries_to_path(gpd.GeoSeries([geom])), 
                                                         transform=ax.transData))
                    
                    # 添加目标国家边界（突出显示） - 更细的黑色边界
                    ax.add_geometries([geom], crs=ccrs.PlateCarree(), facecolor='none',
                                    edgecolor='#000000', linewidth=0.8, zorder=10)
                else:
                    # 世界地图显示所有国家边界 - 专业样式
                    ax.add_geometries(self.world_gdf.geometry, crs=ccrs.PlateCarree(),
                                    facecolor='none', edgecolor='#333333', linewidth=0.4, zorder=3)

                # 创建紧凑的水平颜色条 - 完全匹配地图宽度
                # 获取轴的位置信息
                pos = ax.get_position()

                # 紧凑的色棒布局，紧贴地图底部
                cbar_ax = fig.add_axes([pos.x0, pos.y0 - 0.06, pos.width, 0.02])

                # 创建专业颜色条
                cbar = fig.colorbar(plt.cm.ScalarMappable(norm=norm, cmap=color_scheme),
                                  cax=cbar_ax, orientation='horizontal')
                cbar.set_label('Monthly Total Precipitation (mm)', fontsize=11, fontweight='normal', color='black')
                cbar.ax.tick_params(labelsize=9, colors='black')

                # 设置色棒边框
                cbar.outline.set_linewidth(0.5)
                cbar.outline.set_edgecolor('black')
                
                # 为特定国家添加比例尺 - 使用当前地图范围
                if country and country != 'World':
                    current_extent = ax.get_extent()
                    print(f"DEBUG: 当前地图范围: {current_extent}")
                    self.add_scale_bar(ax, current_extent, country)
                
                # 设置专业标题 - 类似示例图片的样式
                title_text = f"{country or 'World'} {scenario.upper()} {year}-{month:02d} Precipitation"
                plt.suptitle(title_text, fontsize=14, fontweight='normal', y=0.96, color='black')

                # 保存图像
                img_path = out_dir_path / f"{country}_{scenario}_{year}{month:02d}.png"

                # 优化布局 - 紧凑美观的布局，最小化空白
                plt.subplots_adjust(bottom=0.12, top=0.92, left=0.08, right=0.92)

                # 优化保存设置 - 紧凑布局，最小化空白
                plt.savefig(img_path, dpi=300, bbox_inches='tight', pad_inches=0.1,
                           facecolor='white', edgecolor='none', format='png')
                plt.close(fig)
                
                print(f"INFO: 地图已保存至: {img_path}")
                return str(img_path)
                
        except Exception as e:
            import traceback
            print("ERROR: 绘图过程中出现错误:")
            traceback.print_exc()
            if 'fig' in locals():
                plt.close(fig)
            return None

    def add_scale_bar(self, ax, extent, country_name=None):
        """添加比例尺 - 左下角位置，根据国家视野调整大小"""
        try:
            if not all(np.isfinite(extent)):
                print("DEBUG: 地图范围无效，跳过比例尺")
                return

            minx, miny, maxx, maxy = extent

            # 检查坐标有效性
            if abs(maxx - minx) < 0.1 or abs(maxy - miny) < 0.1:
                print("DEBUG: 地图太小，跳过比例尺")
                return

            # 精确计算地图尺寸 - 考虑纬度影响
            center_lat = (miny + maxy) / 2
            lat_correction = np.cos(np.radians(center_lat))

            map_width_km = (maxx - minx) * 111 * lat_correction
            map_height_km = (maxy - miny) * 111

            # 智能选择比例尺长度 - 参考意大利地图的200km标准
            # 目标：比例尺长度约为地图宽度的10-15%
            target_ratio = 0.12
            raw_scale_km = map_width_km * target_ratio

            # 将比例尺长度规范化为标准值
            if raw_scale_km <= 50:
                scale_km = 50
            elif raw_scale_km <= 100:
                scale_km = 100
            elif raw_scale_km <= 200:
                scale_km = 200
            elif raw_scale_km <= 300:
                scale_km = 300
            elif raw_scale_km <= 500:
                scale_km = 500
            elif raw_scale_km <= 1000:
                scale_km = 1000
            else:
                scale_km = 2000

            # 在地图框内的左下角位置
            margin_x = (maxx - minx) * 0.05  # 5%边距
            margin_y = (maxy - miny) * 0.08  # 8%边距

            scale_start_lon = minx + margin_x
            scale_lat = miny + margin_y

            # 计算比例尺长度（经度跨度）
            scale_lon_span = scale_km / (111 * np.cos(np.radians(abs(scale_lat))))  # 使用绝对值避免负数
            scale_end_lon = scale_start_lon + scale_lon_span

            # 确保比例尺不超出地图框
            if scale_end_lon > maxx - margin_x:
                scale_end_lon = maxx - margin_x
                scale_start_lon = scale_end_lon - scale_lon_span
                if scale_start_lon < minx + margin_x:
                    # 如果还是超出，缩短比例尺
                    scale_start_lon = minx + margin_x
                    scale_end_lon = maxx - margin_x
                    scale_km = abs((scale_end_lon - scale_start_lon) * 111 * np.cos(np.radians(abs(scale_lat))))

            print(f"DEBUG: 地图框内比例尺 - {country_name}: [{scale_start_lon:.2f}, {scale_end_lon:.2f}], 纬度: {scale_lat:.2f}, 长度: {int(scale_km)}km")

            # 绘制简单的横线比例尺 - 在地图框内左下角
            scale_line_width = 2.0  # 适中粗细

            # 只绘制一条简单的横线
            ax.plot([scale_start_lon, scale_end_lon], [scale_lat, scale_lat],
                   'k-', linewidth=scale_line_width, transform=ccrs.PlateCarree(), zorder=15)

            # 添加距离标注 - 确保文本不超出地图边界
            # 计算文本位置，确保在地图范围内
            text_x = scale_start_lon + (scale_end_lon - scale_start_lon) * 0.5  # 居中显示
            text_y = scale_lat + (maxy - miny) * 0.02  # 稍微上移

            ax.text(text_x, text_y, f'{int(scale_km)} km',
                   ha='center', va='bottom', fontsize=9, fontweight='normal', color='black',
                   transform=ccrs.PlateCarree(), zorder=16)

            print(f"DEBUG: 左下角比例尺添加成功: {int(scale_km)} km")
            
        except Exception as e:
            print(f"DEBUG: 添加比例尺时出错: {e}")
            # 不打印traceback，避免信息过多

    def batch_extract_and_plot(self, countries, scenarios, years, months, output_dir="batch_output", color_scheme='viridis'):
        """批量提取和绘图功能"""
        results = []
        total_tasks = len(countries) * len(scenarios) * len(years) * len(months)
        current_task = 0
        
        for country in countries:
            for scenario in scenarios:
                for year in years:
                    for month in months:
                        current_task += 1
                        print(f"\n进度: {current_task}/{total_tasks} - 处理 {country} {scenario} {year}-{month:02d}")
                        
                        try:
                            # 提取数据
                            df, csv_file, error = self.extract_data(scenario, year, month, country, output_dir)
                            
                            if error:
                                print(f"错误: {error}")
                                results.append({
                                    'country': country, 'scenario': scenario, 'year': year, 'month': month,
                                    'status': 'failed', 'error': error, 'csv_file': None, 'image_file': None
                                })
                                continue
                            
                            # 绘制地图
                            img_path = self.plot_heatmap_precise(df, scenario, year, month, country, output_dir, color_scheme)
                            
                            if img_path:
                                results.append({
                                    'country': country, 'scenario': scenario, 'year': year, 'month': month,
                                    'status': 'success', 'error': None, 'csv_file': csv_file, 'image_file': img_path
                                })
                                print(f"成功: 数据和地图已生成")
                            else:
                                results.append({
                                    'country': country, 'scenario': scenario, 'year': year, 'month': month,
                                    'status': 'plot_failed', 'error': '地图生成失败', 'csv_file': csv_file, 'image_file': None
                                })
                                print(f"警告: 数据提取成功但地图生成失败")
                                
                        except Exception as e:
                            print(f"异常: {e}")
                            results.append({
                                'country': country, 'scenario': scenario, 'year': year, 'month': month,
                                'status': 'exception', 'error': str(e), 'csv_file': None, 'image_file': None
                            })
        
        # 保存批处理结果汇总
        results_df = pd.DataFrame(results)
        summary_file = os.path.join(output_dir, 'batch_processing_summary.csv')
        results_df.to_csv(summary_file, index=False)
        
        # 打印汇总统计
        success_count = len(results_df[results_df['status'] == 'success'])
        print(f"\n批处理完成!")
        print(f"总任务数: {total_tasks}")
        print(f"成功: {success_count}")
        print(f"失败: {total_tasks - success_count}")
        print(f"成功率: {success_count/total_tasks*100:.1f}%")
        print(f"结果汇总已保存至: {summary_file}")
        
        return results_df

# 主程序入口
if __name__ == "__main__":
    print("请使用 run_interactive.py 来运行交互式程序")
