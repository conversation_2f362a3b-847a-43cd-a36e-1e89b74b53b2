import pandas as pd
import geopandas as gpd
import matplotlib.pyplot as plt
from predict_with_rainfall import predict_spore_risk
from datetime import datetime
import os

def get_available_countries():
    """获取可用的国家列表"""
    shp_path = "vertify/ne_10m_admin_0_countries.shp"
    try:
        world = gpd.read_file(shp_path)
        print(f"成功加载地图文件，包含 {len(world)} 个国家")
        print(f"国家名称示例: {world['NAME'].head().tolist()}")
        return sorted(world["NAME"].tolist())
    except Exception as e:
        print(f"读取地图文件失败: {e}")
        print(f"尝试的路径: {os.path.abspath(shp_path)}")
        return []

def get_available_files():
    """获取当前目录下的CSV文件"""
    csv_files = [f for f in os.listdir(".") if f.endswith('.csv')]
    return sorted(csv_files)

def visualize_risk_heatmap(csv_path, country_name, month, save_plot=True, output_dir="."):
    """
    csv_path: 包含经纬度、日期、降雨量的csv文件
    country_name: 国家英文名（如 'China'）
    month: 需要可视化的月份（如 '2028-01-01' 或 '2028/1/1'）
    save_plot: 是否保存图片到文件
    output_dir: 图片保存目录
    """
    # 1. 读取数据
    df = pd.read_csv(csv_path)
    
    # 2. 统一日期格式处理
    # 将输入的日期转换为数据文件中的格式
    if '/' in month:
        # 如果输入已经是 2028/1/1 格式，直接使用
        target_month = month
    else:
        # 如果输入是 2028-01-01 格式，转换为 2028/1/1 格式
        date_obj = pd.to_datetime(month)
        target_month = f"{date_obj.year}/{date_obj.month}/{date_obj.day}"
    
    # 只保留指定月份
    df_month = df[df['Date'] == target_month].copy()
    if df_month.empty:
        print(f"没有找到 {target_month} 的数据")
        print(f"数据文件中可用的日期格式示例：{df['Date'].head().tolist()}")
        return

    # 3. 预测风险等级
    # predict_spore_risk 支持批量输入
    print(f"开始预测，数据点数量: {len(df_month)}")
    print(f"降雨量范围: {df_month['Rain'].min():.2f} - {df_month['Rain'].max():.2f}")
    
    res = predict_spore_risk(df_month['Date'], df_month['Rain'])
    
    print(f"预测完成，结果形状: {res.shape}")
    print(f"预测结果列: {list(res.columns)}")
    print(f"孢子数范围: {res['孢子数'].min():.2f} - {res['孢子数'].max():.2f}")
    print(f"风险等级范围: {res['风险等级'].min()} - {res['风险等级'].max()}")
    
    df_month['risk'] = res['风险等级'].values
    df_month['spores'] = res['孢子数'].values  # 添加原始孢子数

    # 4. 计算国家平均风险等级和孢子数
    country_avg_risk = df_month['risk'].mean()
    country_avg_spores = df_month['spores'].mean()
    
    print(f"国家平均风险等级: {country_avg_risk:.2f}")
    print(f"国家平均孢子数: {country_avg_spores:.2f}")

    # 5. 读取国家边界
    shp_path = "vertify/ne_10m_admin_0_countries.shp"
    world = gpd.read_file(shp_path)
    country = world[world["NAME"] == country_name]
    if country.empty:
        print(f"未找到国家：{country_name}")
        return

    # 6. 可视化
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 自定义3色配色方案
    colors = ['#2E8B57', '#FFD700', '#DC143C']  # 绿色(低风险), 金色(中风险), 红色(高风险)
    
    # 根据平均风险等级选择颜色
    if country_avg_risk <= 1.5:
        fill_color = colors[0]  # 绿色
        risk_text = "Low Risk"
    elif country_avg_risk <= 2.5:
        fill_color = colors[1]  # 金色
        risk_text = "Medium Risk"
    else:
        fill_color = colors[2]  # 红色
        risk_text = "High Risk"
    
    # 绘制国家形状并填充颜色
    country.plot(ax=ax, color=fill_color, alpha=0.7, edgecolor="black", linewidth=2)
    
    # 添加等级说明（英文）
    level_info = f"""
Spore Level Description:
• Level 1: Spores 0-1000 (Low Risk)
• Level 2: Spores 1000-100000 (Medium Risk)  
• Level 3: Spores 100000-10000000 (High Risk)

Country Statistics:
• Average Risk Level: {country_avg_risk:.2f}
• Average Spores: {country_avg_spores:.0f}
• Risk Status: {risk_text}
    """
    
    # 在图上添加等级说明
    ax.text(0.02, 0.98, level_info, transform=ax.transAxes, 
            fontsize=10, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='white', alpha=0.9))
    
    ax.set_title(f"{country_name} {target_month} Spore Risk Map\n(Based on Predicted Spore Level)", fontsize=14, fontweight='bold')
    plt.axis("off")
    
    # 7. 保存图片
    if save_plot:
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成文件名
        safe_country = country_name.replace(" ", "_")
        safe_date = target_month.replace("/", "_")
        filename = f"{safe_country}_{safe_date}_risk_heatmap.png"
        filepath = os.path.join(output_dir, filename)
        
        # 保存图片
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        print(f"图片已保存到: {os.path.abspath(filepath)}")
    
    plt.show()

def interactive_input():
    """交互式输入函数"""
    print("=" * 50)
    print("孢子风险热力图生成器")
    print("=" * 50)
    
    # 1. 选择数据文件
    print("\n1. 选择数据文件:")
    csv_files = get_available_files()
    if not csv_files:
        print("当前目录下没有找到CSV文件！")
        return None, None, None
    
    print("可用的CSV文件:")
    for i, file in enumerate(csv_files, 1):
        print(f"  {i}. {file}")
    
    while True:
        try:
            choice = int(input(f"\n请选择文件编号 (1-{len(csv_files)}): "))
            if 1 <= choice <= len(csv_files):
                csv_path = csv_files[choice - 1]
                break
            else:
                print("无效的选择，请重新输入")
        except ValueError:
            print("请输入数字")
    
    # 2. 选择国家
    print("\n2. 选择国家:")
    countries = get_available_countries()
    if not countries:
        print("无法获取国家列表！")
        return None, None, None
    
    print("可用的国家 (显示前20个):")
    for i, country in enumerate(countries[:20], 1):
        print(f"  {i}. {country}")
    
    if len(countries) > 20:
        print(f"  ... 还有 {len(countries) - 20} 个国家")
    
    while True:
        try:
            choice = int(input(f"\n请选择国家编号 (1-{len(countries)}): "))
            if 1 <= choice <= len(countries):
                country_name = countries[choice - 1]
                break
            else:
                print("无效的选择，请重新输入")
        except ValueError:
            print("请输入数字")
    
    # 3. 输入年份和月份
    print("\n3. 输入年份和月份:")
    while True:
        try:
            year = int(input("请输入年份 (如 2028): "))
            if 1900 <= year <= 2100:
                break
            else:
                print("年份范围应在1900-2100之间")
        except ValueError:
            print("请输入有效的年份")
    
    while True:
        try:
            month = int(input("请输入月份 (1-12): "))
            if 1 <= month <= 12:
                break
            else:
                print("月份范围应在1-12之间")
        except ValueError:
            print("请输入有效的月份")
    
    # 构造日期字符串
    month_str = f"{year}/{month}/1"
    
    print(f"\n选择的参数:")
    print(f"  数据文件: {csv_path}")
    print(f"  国家: {country_name}")
    print(f"  日期: {month_str}")
    
    return csv_path, country_name, month_str

if __name__ == "__main__":
    # 交互式输入
    csv_path, country_name, month = interactive_input()
    
    if csv_path and country_name and month:
        print(f"\n开始生成热力图...")
        try:
            visualize_risk_heatmap(csv_path, country_name, month, save_plot=True, output_dir=".")
            print("热力图生成完成！")
        except Exception as e:
            print(f"生成失败: {e}")
    else:
        print("输入参数不完整，程序退出")