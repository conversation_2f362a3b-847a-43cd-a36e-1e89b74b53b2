#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CMIP6降雨数据提取工具 - 终端交互版本
支持用户选择场景、年份、月份、国家，提取数据并生成热图
"""

import os
import sys
from pathlib import Path
# 确保在vertify文件夹内运行，以便正确导入
script_dir = os.path.dirname(os.path.abspath(__file__))
if script_dir not in sys.path:
    sys.path.insert(0, script_dir)

from extract_country_data import CountryRainfallExtractor


def display_menu():
    """显示主菜单"""
    print("\n" + "="*60)
    print("CMIP6 Monthly Rainfall Extractor - Terminal Version")
    print("="*60)
    print("1. 提取指定场景、年份、月份、国家的数据")
    print("2. 查看可用国家列表")
    print("3. 查看可用场景和年份")
    print("4. 退出")
    print("="*60)

def display_scenarios_and_years(extractor):
    """显示可用场景和年份"""
    print("\n可用场景:")
    for i, scenario in enumerate(extractor.scenarios.keys(), 1):
        print(f"  {i}. {scenario.upper()}")
    
    print("\n可用年份:")
    for i, year in enumerate(extractor.target_years, 1):
        print(f"  {i}. {year}")
    
    print("\n可用月份:")
    for i, month in enumerate(extractor.months, 1):
        print(f"  {i}. {month}")

def display_countries(extractor):
    """显示可用国家列表"""
    countries = extractor.get_available_countries()
    if not countries:
        print("\n无法从shapefile中加载国家列表。")
        return

    print(f"\n可从 Shapefile 中选择的国家列表 (共{len(countries)}个):")
    
    # 将 "World" 插入列表以便统一编号
    display_list = ["World"] + countries
    
    # 分列显示
    col_width = 30
    num_cols = 4
    for i in range(0, len(display_list), num_cols):
        line_items = []
        for j in range(num_cols):
            if i + j < len(display_list):
                item = f"{(i + j + 1):>3}. {display_list[i+j]}"
                line_items.append(f"{item:<{col_width}}")
        print("".join(line_items))


def get_user_selection(extractor):
    """获取用户选择"""
    while True:
        display_menu()
        choice = input("\n请选择操作 (1-4): ").strip()
        
        if choice == "1":
            extract_specific_data(extractor)
            return # 返回主循环
        elif choice == "2":
            display_countries(extractor)
            input("\n按回车键继续...")
        elif choice == "3":
            display_scenarios_and_years(extractor)
            input("\n按回车键继续...")
        elif choice == "4":
            print("再见!")
            sys.exit(0)
        else:
            print("无效选择，请重新输入。")

def extract_specific_data(extractor):
    """提取指定数据"""
    print("\n" + "="*50)
    print("数据提取配置")
    print("="*50)

    # 1. 选择场景 (支持多选)
    scenarios = list(extractor.scenarios.keys())
    print("\n可用场景:")
    for i, scenario in enumerate(scenarios, 1):
        print(f"  {i}. {scenario.upper()}")
    print("场景选择方式: 1.单个(1) 2.多个(1,3,4) 3.所有(all)")

    selected_scenarios = []
    while True:
        scenario_input = input(f"\n请选择场景 (1-{len(scenarios)}): ").strip()
        if scenario_input.lower() == 'all':
            selected_scenarios = scenarios
            break
        elif ',' in scenario_input:
            try:
                choices = [int(c.strip()) for c in scenario_input.split(',')]
                selected_scenarios = [scenarios[c-1] for c in choices if 1 <= c <= len(scenarios)]
                if selected_scenarios:
                    break
                else:
                    print("无效选择，请重新输入。")
            except (ValueError, IndexError):
                print("格式错误，请使用如 '1,3,4' 的格式。")
        else:
            try:
                scenario_choice = int(scenario_input) - 1
                if 0 <= scenario_choice < len(scenarios):
                    selected_scenarios = [scenarios[scenario_choice]]
                    break
                else:
                    print("无效选择，请重新输入。")
            except ValueError:
                print("请输入数字。")
    print(f"✓ 已选择场景: {[s.upper() for s in selected_scenarios]}")

    # 2. 选择年份 (支持多选)
    print(f"\n可用年份: {extractor.target_years}")
    print("年份选择方式: 1.单年(2025) 2.连续(2025-2027) 3.多个(2025,2027) 4.所有(all)")

    selected_years = []
    while True:
        year_input = input("请选择年份: ").strip()
        if year_input.lower() == 'all':
            selected_years = extractor.target_years
            break
        elif '-' in year_input:
            try:
                start, end = map(int, year_input.split('-'))
                selected_years = [y for y in range(start, end + 1) if y in extractor.target_years]
                if selected_years:
                    break
                else:
                    print(f"无效年份范围，请从 {extractor.target_years} 中选择。")
            except Exception:
                print("格式错误，请使用如 '2025-2027' 的格式。")
        elif ',' in year_input:
            try:
                selected_years = [int(y.strip()) for y in year_input.split(',') if int(y.strip()) in extractor.target_years]
                if selected_years:
                    break
                else:
                    print(f"无效年份，请从 {extractor.target_years} 中选择。")
            except Exception:
                print("格式错误，请使用如 '2025,2027' 的格式。")
        else:
            try:
                year = int(year_input)
                if year in extractor.target_years:
                    selected_years = [year]
                    break
                else:
                    print(f"无效年份，请从 {extractor.target_years} 中选择。")
            except ValueError:
                print("请输入数字。")
    print(f"✓ 已选择年份: {selected_years}")

    # 3. 选择月份 (支持多选)
    print(f"\n可用月份: 1-12")
    print("月份选择方式: 1.单月(6) 2.连续(6-8) 3.多个(6,8,12) 4.所有(all)")

    while True:
        month_input = input("请选择月份: ").strip()
        if month_input.lower() == 'all':
            months = extractor.months
            break
        elif '-' in month_input:
            try:
                start, end = map(int, month_input.split('-'))
                months = list(range(start, end + 1))
                if all(1 <= m <= 12 for m in months):
                    break
                else:
                    print("月份必须在1-12范围内。")
            except Exception:
                print("格式错误，请使用如 '6-8' 的格式。")
        elif ',' in month_input:
            try:
                months = [int(m.strip()) for m in month_input.split(',')]
                if all(1 <= m <= 12 for m in months):
                    break
                else:
                    print("月份必须在1-12范围内。")
            except Exception:
                print("格式错误，请使用如 '6,8,12' 的格式。")
        else:
            try:
                month = int(month_input)
                if 1 <= month <= 12:
                    months = [month]
                    break
                else:
                    print("月份必须在1-12范围内。")
            except ValueError:
                print("请输入有效的月份。")
    print(f"✓ 已选择月份: {months}")

    # 4. 选择国家 (支持多选)
    available_countries = extractor.get_available_countries()
    if not available_countries:
        print("\n错误: 无法从 shapefile 加载国家列表，无法继续。")
        return

    display_list = ["World"] + available_countries

    print("\n" + "="*50)
    print("国家选择 (可多选, 用英文逗号','隔开)")
    print("="*50)

    col_width = 30
    num_cols = 4
    for i in range(0, len(display_list), num_cols):
        line_items = []
        for j in range(num_cols):
            if i + j < len(display_list):
                item = f"{(i + j + 1):>3}. {display_list[i+j]}"
                line_items.append(f"{item:<{col_width}}")
        print("".join(line_items))

    print("-" * 50)
    print("国家选择方式: 1.单个(1) 2.多个(1,5,10) 3.所有(all)")

    selected_countries = []
    while True:
        try:
            choice_str = input(f"请选择国家 (1-{len(display_list)}): ").strip()
            if not choice_str:
                print("请输入至少一个选项。")
                continue

            if choice_str.lower() == 'all':
                selected_countries = display_list
                break

            choices = [int(c.strip()) for c in choice_str.split(',')]

            valid_choices = True
            selected_countries_temp = []
            for choice in choices:
                if 1 <= choice <= len(display_list):
                    selected_countries_temp.append(display_list[choice - 1])
                else:
                    print(f"无效选择: {choice}。请输入列表中的数字。")
                    valid_choices = False
                    break

            if valid_choices:
                selected_countries = list(dict.fromkeys(selected_countries_temp)) # Remove duplicates
                break

        except ValueError:
            print("无效输入，请输入数字，并用逗号隔开。")

    print(f"✓ 已选择国家: {', '.join(selected_countries)}")

    # 5. 选择颜色方案
    color_schemes = ['viridis', 'plasma', 'coolwarm', 'RdYlBu_r', 'Spectral_r', 'Blues', 'YlOrRd']
    print("\n颜色方案:")
    for i, cs in enumerate(color_schemes, 1):
        print(f"  {i}. {cs}")
    while True:
        cs_choice = input(f"请选择 (1-{len(color_schemes)}, 默认1): ").strip()
        if cs_choice == '':
            color_scheme = color_schemes[0]
            break
        try:
            cs_index = int(cs_choice) - 1
            if 0 <= cs_index < len(color_schemes):
                color_scheme = color_schemes[cs_index]
                break
            else:
                print("无效选择。")
        except ValueError:
            print("请输入数字。")
    print(f"✓ 已选择颜色方案: {color_scheme}")

    # 确认选择
    print(f"\n--- 确认选择 ---\n"
          f"  场景: {[s.upper() for s in selected_scenarios]}\n"
          f"  年份: {selected_years}\n"
          f"  月份: {months}\n"
          f"  国家: {', '.join(selected_countries)}\n"
          f"  颜色: {color_scheme}\n"
          f"--------------------")

    if input("确认提取数据? (y/n, 默认y): ").strip().lower() == 'n':
        print("已取消操作。")
        return
    
    # 执行批量提取
    print(f"\n>>> 开始批量提取任务...")
    print(f"总任务数: {len(selected_scenarios)} 场景 × {len(selected_years)} 年份 × {len(months)} 月份 × {len(selected_countries)} 国家 = {len(selected_scenarios) * len(selected_years) * len(months) * len(selected_countries)} 个任务")

    # 使用批量处理功能
    try:
        results_df = extractor.batch_extract_and_plot(
            countries=selected_countries,
            scenarios=selected_scenarios,
            years=selected_years,
            months=months,
            output_dir="extracted_data",
            color_scheme=color_scheme
        )

        print(f"\n{'='*60}")
        print("批量处理完成！")
        print(f"{'='*60}")

        # 显示详细结果统计
        success_count = len(results_df[results_df['status'] == 'success'])
        failed_count = len(results_df[results_df['status'] != 'success'])

        print(f"✓ 成功完成: {success_count} 个任务")
        print(f"✗ 失败任务: {failed_count} 个任务")
        print(f"成功率: {success_count/(success_count+failed_count)*100:.1f}%")

        # 显示输出文件位置
        print(f"\n输出文件位置:")
        print(f"  - CSV数据文件: extracted_data/")
        print(f"  - 地图图片文件: extracted_data/")
        print(f"  - 批处理汇总: extracted_data/batch_processing_summary.csv")

        # 显示失败任务详情（如果有）
        if failed_count > 0:
            print(f"\n失败任务详情:")
            failed_tasks = results_df[results_df['status'] != 'success']
            for _, task in failed_tasks.iterrows():
                print(f"  - {task['country']} {task['scenario']} {task['year']}-{task['month']:02d}: {task['error']}")

    except Exception as e:
        import traceback
        print(f"\n!!! 批量处理时发生严重错误: {e}")
        traceback.print_exc()

def main():
    """主函数"""
    print("正在初始化CMIP6降雨数据提取器...")
    
    # 切换到脚本所在目录以保证相对路径正确
    os.chdir(script_dir)
    print(f"当前工作目录: {os.getcwd()}")
    
    # 配置路径
    tif_dir = script_dir
    shp_dir = os.path.join(script_dir, '全球国家边界_按照国家分SHP')
    
    print(f"TIF数据根目录: {tif_dir}")
    print(f"Shapefile目录: {shp_dir}")
    
    try:
        extractor = CountryRainfallExtractor(str(tif_dir), str(shp_dir))
        print("✓ 初始化完成!")
        
        while True:
            get_user_selection(extractor)
    
    except Exception as e:
        print(f"\n!!! 初始化失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
