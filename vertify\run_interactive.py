#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CMIP6降雨数据提取工具 - 终端交互版本
支持用户选择场景、年份、月份、国家，提取数据并生成热图
"""

import os
import sys
from pathlib import Path
# 确保在vertify文件夹内运行，以便正确导入
script_dir = os.path.dirname(os.path.abspath(__file__))
if script_dir not in sys.path:
    sys.path.insert(0, script_dir)

from extract_country_data import CountryRainfallExtractor


def display_menu():
    """显示主菜单"""
    print("\n" + "="*60)
    print("CMIP6 Monthly Rainfall Extractor - Terminal Version")
    print("="*60)
    print("1. 提取指定场景、年份、月份、国家的数据")
    print("2. 查看可用国家列表")
    print("3. 查看可用场景和年份")
    print("4. 退出")
    print("="*60)

def display_scenarios_and_years(extractor):
    """显示可用场景和年份"""
    print("\n可用场景:")
    for i, scenario in enumerate(extractor.scenarios.keys(), 1):
        print(f"  {i}. {scenario.upper()}")
    
    print("\n可用年份:")
    for i, year in enumerate(extractor.target_years, 1):
        print(f"  {i}. {year}")
    
    print("\n可用月份:")
    for i, month in enumerate(extractor.months, 1):
        print(f"  {i}. {month}")

def display_countries(extractor):
    """显示可用国家列表"""
    countries = extractor.get_available_countries()
    if not countries:
        print("\n无法从shapefile中加载国家列表。")
        return

    print(f"\n可从 Shapefile 中选择的国家列表 (共{len(countries)}个):")
    
    # 将 "World" 插入列表以便统一编号
    display_list = ["World"] + countries
    
    # 分列显示
    col_width = 30
    num_cols = 4
    for i in range(0, len(display_list), num_cols):
        line_items = []
        for j in range(num_cols):
            if i + j < len(display_list):
                item = f"{(i + j + 1):>3}. {display_list[i+j]}"
                line_items.append(f"{item:<{col_width}}")
        print("".join(line_items))


def get_user_selection(extractor):
    """获取用户选择"""
    while True:
        display_menu()
        choice = input("\n请选择操作 (1-4): ").strip()
        
        if choice == "1":
            extract_specific_data(extractor)
            return # 返回主循环
        elif choice == "2":
            display_countries(extractor)
            input("\n按回车键继续...")
        elif choice == "3":
            display_scenarios_and_years(extractor)
            input("\n按回车键继续...")
        elif choice == "4":
            print("再见!")
            sys.exit(0)
        else:
            print("无效选择，请重新输入。")

def extract_specific_data(extractor):
    """提取指定数据"""
    print("\n" + "="*50)
    print("数据提取配置")
    print("="*50)
    
    # 1. 选择场景
    scenarios = list(extractor.scenarios.keys())
    print("\n可用场景:")
    for i, scenario in enumerate(scenarios, 1):
        print(f"  {i}. {scenario.upper()}")
    
    while True:
        try:
            scenario_choice = int(input(f"\n请选择场景 (1-{len(scenarios)}): ")) - 1
            if 0 <= scenario_choice < len(scenarios):
                scenario = scenarios[scenario_choice]
                break
            else:
                print("无效选择，请重新输入。")
        except ValueError:
            print("请输入数字。")
    
    # 2. 选择年份
    print(f"\n可用年份: {extractor.target_years}")
    while True:
        try:
            year = int(input("请选择年份: "))
            if year in extractor.target_years:
                break
            else:
                print(f"无效年份，请从 {extractor.target_years} 中选择。")
        except ValueError:
            print("请输入数字。")
    
    # 3. 选择月份 (支持多选)
    print(f"\n可用月份: 1-12")
    print("月份选择方式: 1.单月(6) 2.连续(6-8) 3.多个(6,8,12) 4.所有(all)")
    
    while True:
        month_input = input("请选择月份: ").strip()
        if month_input.lower() == 'all':
            months = extractor.months
            break
        elif '-' in month_input:
            try:
                start, end = map(int, month_input.split('-'))
                months = list(range(start, end + 1))
                break
            except Exception: print("格式错误，请使用如 '6-8' 的格式。")
        elif ',' in month_input:
            try:
                months = [int(m.strip()) for m in month_input.split(',')]
                break
            except Exception: print("格式错误，请使用如 '6,8,12' 的格式。")
        else:
            try:
                months = [int(month_input)]
                break
            except ValueError: print("请输入有效的月份。")
    print(f"✓ 已选择月份: {months}")

    # 4. 选择国家 (支持多选)
    available_countries = extractor.get_available_countries()
    if not available_countries:
        print("\n错误: 无法从 shapefile 加载国家列表，无法继续。")
        return

    display_list = ["World"] + available_countries

    print("\n" + "="*50)
    print("国家选择 (可多选, 用英文逗号','隔开)")
    print("="*50)
    
    col_width = 30
    num_cols = 4
    for i in range(0, len(display_list), num_cols):
        line_items = []
        for j in range(num_cols):
            if i + j < len(display_list):
                item = f"{(i + j + 1):>3}. {display_list[i+j]}"
                line_items.append(f"{item:<{col_width}}")
        print("".join(line_items))
    
    print("-" * 50)

    selected_countries = []
    while True:
        try:
            choice_str = input(f"请选择国家 (1-{len(display_list)}): ").strip()
            if not choice_str:
                print("请输入至少一个选项。")
                continue
            
            choices = [int(c.strip()) for c in choice_str.split(',')]
            
            valid_choices = True
            selected_countries_temp = []
            for choice in choices:
                if 1 <= choice <= len(display_list):
                    selected_countries_temp.append(display_list[choice - 1])
                else:
                    print(f"无效选择: {choice}。请输入列表中的数字。")
                    valid_choices = False
                    break
            
            if valid_choices:
                selected_countries = list(dict.fromkeys(selected_countries_temp)) # Remove duplicates
                break
                
        except ValueError:
            print("无效输入，请输入数字，并用逗号隔开。")
            
    print(f"✓ 已选择国家: {', '.join(selected_countries)}")
    
    # 5. 选择绘图方法
    print("\n绘图方法: 1.contourf(平滑) 2.pcolormesh(网格)")
    while True:
        method_choice = input("请选择 (1-2, 默认1): ").strip()
        if method_choice == '2':
            method = 'pcolormesh'
            break
        elif method_choice in ['1', '']:
            method = 'contourf'
            break
        else:
            print("无效输入。")
    print(f"✓ 已选择绘图方法: {method}")

    # 6. 选择颜色方案
    color_schemes = ['viridis', 'plasma', 'coolwarm', 'RdYlBu_r', 'Spectral_r']
    print("\n颜色方案:")
    for i, cs in enumerate(color_schemes, 1): print(f"{i}. {cs}")
    while True:
        cs_choice = input(f"请选择 (1-{len(color_schemes)}, 默认1): ").strip()
        if cs_choice == '':
            color_scheme = color_schemes[0]
            break
        try:
            cs_index = int(cs_choice) - 1
            if 0 <= cs_index < len(color_schemes):
                color_scheme = color_schemes[cs_index]
                break
            else:
                print("无效选择。")
        except ValueError:
            print("请输入数字。")
    print(f"✓ 已选择颜色方案: {color_scheme}")
    
    # 确认选择
    print(f"\n--- 确认选择 ---\n"
          f"  场景: {scenario.upper()}\n"
          f"  年份: {year}\n"
          f"  月份: {months}\n"
          f"  国家: {', '.join(selected_countries)}\n"
          f"  方法: {method}\n"
          f"  颜色: {color_scheme}\n"
          f"--------------------")
    
    if input("确认提取数据? (y/n, 默认y): ").strip().lower() == 'n':
        print("已取消操作。")
        return
    
    # 执行提取
    print(f"\n>>> 开始批量提取任务...")
    
    for country_name_input in selected_countries:
        print(f"\n{'='*20} 正在处理: {country_name_input} {'='*20}")
        try:
            if len(months) == 1:
                df, csv_file, error = extractor.extract_data(scenario, year, months[0], country_name_input)
                if error:
                    print(f"✗ 错误: {error}")
                    continue
                if df is not None:
                    extractor.plot_heatmap_precise(df, scenario, year, months[0], country_name_input, "extracted_data", color_scheme)
            else:
                df, csv_files, img_files, error = extractor.extract_multiple_months(scenario, year, months, country_name_input, "extracted_data", color_scheme)
                if error:
                    print(f"✗ 错误: {error}")
                    continue
                if df is not None:
                    # 为多月份处理添加绘图功能
                    for month in months:
                        try:
                            extractor.plot_heatmap_precise(df, scenario, year, month, country_name_input, "extracted_data", color_scheme)
                        except Exception as e:
                            print(f"✗ 绘制 {month} 月图片时出错: {e}")
                print(f"\n多月份处理完成 for {country_name_input}。")

        except Exception as e:
            import traceback
            print(f"\n!!! 处理国家 {country_name_input} 时发生严重错误: {e}")
            traceback.print_exc()

def main():
    """主函数"""
    print("正在初始化CMIP6降雨数据提取器...")
    
    # 切换到脚本所在目录以保证相对路径正确
    os.chdir(script_dir)
    print(f"当前工作目录: {os.getcwd()}")
    
    # 配置路径
    tif_dir = script_dir
    shp_dir = os.path.join(script_dir, '全球国家边界_按照国家分SHP')
    
    print(f"TIF数据根目录: {tif_dir}")
    print(f"Shapefile目录: {shp_dir}")
    
    try:
        extractor = CountryRainfallExtractor(str(tif_dir), str(shp_dir))
        print("✓ 初始化完成!")
        
        while True:
            get_user_selection(extractor)
    
    except Exception as e:
        print(f"\n!!! 初始化失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
