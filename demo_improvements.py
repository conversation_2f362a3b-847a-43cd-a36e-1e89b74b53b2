#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示CMIP6工具的两个核心改进：
1. 国家选择改为编号方式
2. 修复地图显示范围问题
"""

from pathlib import Path
import os

class CMIP6Improvements:
    def __init__(self):
        self.base_dir = Path(__file__).parent / "vertify"
        
        # 预定义国家列表
        self.countries = [
            "World",
            "China", 
            "United States",
            "Brazil", 
            "India",
            "Russia",
            "Canada",
            "Australia", 
            "New Zealand",
            "Japan",
            "United Kingdom",
            "France",
            "Germany",
            "Italy",
            "Spain"
        ]
        
        # 修复后的国家显示范围（解决地图显示问题）
        self.country_extents = {
            'World': [-180, 180, -90, 90],
            'United States': [-130, -65, 20, 50],  # 美国本土（修复：不再是全世界视野）
            'China': [70, 140, 15, 55],
            'Brazil': [-75, -30, -35, 10],
            'India': [65, 100, 5, 40],
            'Russia': [20, 180, 40, 85],
            'Canada': [-150, -50, 40, 85],
            'Australia': [110, 160, -45, -10],
            'New Zealand': [165, 180, -48, -34],  # 新西兰专用范围
            'Japan': [125, 150, 25, 50],
            'United Kingdom': [-12, 5, 49, 62],
            'France': [-8, 10, 41, 52],
            'Germany': [5, 16, 47, 56],
            'Italy': [6, 19, 36, 48],
            'Spain': [-10, 5, 35, 45],
        }

    def demo_numbered_country_selection(self):
        """演示改进1：编号选择国家"""
        print("\n" + "="*80)
        print("改进1：国家选择改为编号方式")
        print("="*80)
        print("之前：用户需要输入完整的国家名称，容易出错")
        print("现在：显示带编号的国家列表，用户只需输入编号")
        print()
        
        # 显示带编号的国家列表
        print("可用国家列表:")
        col_width = 25
        for i in range(0, len(self.countries), 3):
            line = ""
            for j in range(3):
                if i + j < len(self.countries):
                    item = f"{i+j+1:3d}. {self.countries[i+j]}"
                    line += f"{item:<{col_width}}"
            print(line)
        
        print("\n支持的输入方式:")
        print("  - 单个选择: 3 (选择United States)")
        print("  - 多个选择: 1,3,5 (选择World, United States, India)")
        print("  - 范围选择: 1-5 (选择前5个国家)")
        print("  - 全部选择: all (选择所有国家)")

    def demo_fixed_map_extents(self):
        """演示改进2：修复地图显示范围"""
        print("\n" + "="*80)
        print("改进2：修复地图显示范围问题")
        print("="*80)
        print("之前：美国等国家显示全世界视野，无法看清国家细节")
        print("现在：每个国家都有合适的显示范围，聚焦于国家本身")
        print()
        
        # 展示几个关键国家的修复
        key_countries = ["United States", "China", "New Zealand", "Brazil"]
        
        for country in key_countries:
            extent = self.country_extents[country]
            print(f"{country:15s}: [{extent[0]:6.1f}, {extent[1]:6.1f}, {extent[2]:6.1f}, {extent[3]:6.1f}]")
            
            if country == "United States":
                print("                 ↑ 修复：专注美国本土，不再显示全世界")
            elif country == "New Zealand":
                print("                 ↑ 修复：使用165-180度经度，适合新西兰位置")
        
        print("\n地图范围说明:")
        print("  格式: [最小经度, 最大经度, 最小纬度, 最大纬度]")
        print("  每个国家都有专门优化的显示范围")
        print("  确保地图聚焦于国家本身，而不是全球视野")

    def demo_data_file_detection(self):
        """演示数据文件检测"""
        print("\n" + "="*80)
        print("数据文件检测")
        print("="*80)
        
        # 检查数据文件夹
        scenarios = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
        
        for scenario in scenarios:
            # 检查pr_文件夹（0-360度坐标）
            pr_folder = self.base_dir / f"pr_{scenario}_202501-202812"
            pr_count = len(list(pr_folder.glob("*.tif"))) if pr_folder.exists() else 0
            
            # 检查2pr_文件夹（-180到180度坐标）
            pr2_folder = self.base_dir / f"2pr_{scenario}_202501-202812"
            pr2_count = len(list(pr2_folder.glob("*.tif"))) if pr2_folder.exists() else 0
            
            print(f"{scenario:6s}: pr_文件夹({pr_count:2d}个文件) + 2pr_文件夹({pr2_count:2d}个文件)")
        
        print("\n文件夹说明:")
        print("  pr_文件夹:  0-360度坐标系，适用于新西兰、美国等跨180度经线的国家")
        print("  2pr_文件夹: -180到180度坐标系，适用于其他大部分国家")

    def demo_parsing_logic(self):
        """演示输入解析逻辑"""
        print("\n" + "="*80)
        print("输入解析逻辑演示")
        print("="*80)
        
        test_inputs = [
            "3",           # 单个选择
            "1,3,5",       # 多个选择
            "1-5",         # 范围选择
            "1,3,7-9",     # 混合选择
            "all"          # 全部选择
        ]
        
        for input_str in test_inputs:
            result = self.parse_selection(input_str, len(self.countries))
            selected = [self.countries[i-1] for i in result]
            print(f"输入 '{input_str:8s}' -> 编号 {result} -> 国家 {selected}")

    def parse_selection(self, input_str, max_num):
        """解析用户输入的选择"""
        selections = []
        
        if input_str.lower() == 'all':
            return list(range(1, max_num + 1))
        
        parts = input_str.split(',')
        
        for part in parts:
            part = part.strip()
            if '-' in part:
                try:
                    start, end = map(int, part.split('-'))
                    selections.extend(range(start, end + 1))
                except:
                    continue
            else:
                try:
                    num = int(part)
                    if 1 <= num <= max_num:
                        selections.append(num)
                except:
                    continue
        
        return sorted(list(set(selections)))

def main():
    """主演示函数"""
    print("🌧️  CMIP6降雨数据可视化工具 - 核心改进演示")
    
    demo = CMIP6Improvements()
    
    # 演示改进1：编号选择国家
    demo.demo_numbered_country_selection()
    
    # 演示改进2：修复地图显示范围
    demo.demo_fixed_map_extents()
    
    # 演示数据文件检测
    demo.demo_data_file_detection()
    
    # 演示输入解析逻辑
    demo.demo_parsing_logic()
    
    print("\n" + "="*80)
    print("演示完成！")
    print("核心改进总结:")
    print("1. ✅ 国家选择改为编号方式 - 更简单、更不容易出错")
    print("2. ✅ 修复地图显示范围问题 - 每个国家都有合适的显示范围")
    print("="*80)

if __name__ == "__main__":
    main()
