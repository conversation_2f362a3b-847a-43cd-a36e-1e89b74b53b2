#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CMIP6降雨数据可视化工具 - 依赖安装脚本
自动安装所需的Python库
"""

import subprocess
import sys
import os

def check_pip():
    """检查pip是否可用"""
    try:
        subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                      check=True, capture_output=True)
        return True
    except subprocess.CalledProcessError:
        return False

def install_package(package):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                               check=True, capture_output=True, text=True)
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e.stderr}")
        return False

def main():
    """主函数"""
    print("🚀 CMIP6降雨数据可视化工具 - 依赖安装程序")
    print("="*60)
    
    # 检查pip
    if not check_pip():
        print("❌ 错误: pip不可用")
        print("请确保Python和pip正确安装")
        input("按回车键退出...")
        return
    
    print("✅ pip检查通过")
    
    # 要安装的包列表
    packages = [
        'numpy>=1.19.0',
        'pandas>=1.3.0', 
        'geopandas>=0.10.0',
        'rasterio>=1.2.0',
        'shapely>=1.7.0',
        'matplotlib>=3.3.0',
        'cartopy>=0.20.0',
        'scipy>=1.7.0',
        'fiona>=1.8.0'
    ]
    
    print(f"\n将安装 {len(packages)} 个依赖包...")
    print("-"*60)
    
    # 安装包
    success_count = 0
    failed_packages = []
    
    for package in packages:
        if install_package(package):
            success_count += 1
        else:
            failed_packages.append(package)
    
    # 汇总结果
    print("\n" + "="*60)
    print("📊 安装结果汇总")
    print("="*60)
    print(f"✅ 成功安装: {success_count}/{len(packages)} 个包")
    
    if failed_packages:
        print(f"❌ 安装失败的包:")
        for pkg in failed_packages:
            print(f"   - {pkg}")
        
        print(f"\n💡 故障排除建议:")
        print(f"1. 检查网络连接")
        print(f"2. 尝试升级pip: python -m pip install --upgrade pip")
        print(f"3. 使用国内镜像: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/")
        print(f"4. 手动安装失败的包")
    else:
        print("🎉 所有依赖包安装成功!")
        
        # 运行测试
        print(f"\n🧪 正在运行系统测试...")
        try:
            import test_cmip6_tool
            test_result = test_cmip6_tool.run_all_tests()
            
            if test_result:
                print(f"\n🚀 安装完成! 系统测试通过")
                print(f"现在可以运行主程序了:")
                print(f"  - 双击 '启动CMIP6工具.bat' (Windows)")
                print(f"  - 或运行 'python start_cmip6_tool.py'")
            else:
                print(f"\n⚠️  依赖安装成功，但系统测试未完全通过")
                print(f"请检查数据文件是否完整")
                
        except Exception as e:
            print(f"\n⚠️  依赖安装成功，但无法运行系统测试: {e}")
            print(f"请手动运行 'python test_cmip6_tool.py' 进行测试")
    
    input(f"\n按回车键退出...")

if __name__ == "__main__":
    main()
