#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试CLI工具的核心功能
"""

import sys
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_tool_initialization():
    """测试工具初始化"""
    print("="*60)
    print("测试CMIP6工具初始化")
    print("="*60)
    
    try:
        # 导入工具类
        from cmip6_rainfall_cli import CMIP6RainfallTool
        
        print("1. 正在初始化工具...")
        tool = CMIP6RainfallTool()
        print("✅ 工具初始化成功")
        
        print("2. 获取可用国家...")
        countries = tool.get_available_countries()
        print(f"✅ 找到 {len(countries)} 个国家")
        
        # 显示前10个国家
        print("\n前10个国家:")
        for i, country in enumerate(countries[:10], 1):
            print(f"  {i:2d}. {country}")
        
        print("3. 测试国家显示范围...")
        test_countries = ["China", "United States", "Brazil", "World"]
        
        for country in test_countries:
            try:
                extent = tool.get_country_display_extent(country)
                print(f"  {country:15s}: {extent}")
            except Exception as e:
                print(f"  {country:15s}: 错误 - {e}")
        
        print("4. 测试数据文件检测...")
        test_scenario = "ssp126"
        test_year = 2025
        test_month = 6
        test_country = "China"
        
        try:
            data, transform, crs = tool.load_tif_data(test_scenario, test_year, test_month, test_country)
            print(f"✅ 数据加载成功: {data.shape}, CRS: {crs}")
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
        
        print("\n" + "="*60)
        print("测试完成!")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_numbered_selection():
    """测试编号选择功能"""
    print("\n" + "="*60)
    print("测试编号选择功能")
    print("="*60)
    
    try:
        from cmip6_rainfall_cli import parse_list_input
        
        test_cases = [
            ("3", "单个选择"),
            ("1,3,5", "多个选择"),
            ("1-5", "范围选择"),
            ("1,3,7-9", "混合选择"),
            ("all", "全部选择")
        ]
        
        available_items = list(range(1, 16))  # 1-15
        
        for input_str, description in test_cases:
            try:
                result = parse_list_input(input_str, available_items)
                print(f"  {description:10s} '{input_str:8s}' -> {result}")
            except Exception as e:
                print(f"  {description:10s} '{input_str:8s}' -> 错误: {e}")
        
        print("✅ 编号选择功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 编号选择测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🌧️  CMIP6降雨数据可视化工具 - 功能测试")
    
    # 测试工具初始化
    init_ok = test_tool_initialization()
    
    # 测试编号选择
    selection_ok = test_numbered_selection()
    
    print(f"\n" + "="*60)
    print("总体测试结果:")
    print(f"  工具初始化: {'✅ 通过' if init_ok else '❌ 失败'}")
    print(f"  编号选择: {'✅ 通过' if selection_ok else '❌ 失败'}")
    
    if init_ok and selection_ok:
        print("\n🎉 所有核心功能测试通过!")
        print("您现在可以使用以下命令启动工具:")
        print("  python cmip6_rainfall_cli.py --interactive")
    else:
        print("\n❌ 部分功能测试失败，请检查环境配置")
    
    print("="*60)

if __name__ == "__main__":
    main()
