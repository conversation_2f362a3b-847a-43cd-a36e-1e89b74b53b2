#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版CMIP6降雨数据可视化工具
专注解决两个核心问题：
1. 国家选择改为编号方式
2. 修复地图显示范围问题
"""

import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import matplotlib.colors as colors
from pathlib import Path
import calendar
import os

class SimpleCMIP6Tool:
    def __init__(self):
        self.base_dir = Path(__file__).parent / "vertify"
        
        # 场景配置
        self.scenarios = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
        self.years = [2025, 2026, 2027, 2028]
        self.months = list(range(1, 13))
        
        # 预定义国家列表（简化版本）
        self.countries = [
            "World",
            "China", 
            "United States",
            "Brazil", 
            "India",
            "Russia",
            "Canada",
            "Australia", 
            "New Zealand",
            "Japan",
            "United Kingdom",
            "France",
            "Germany",
            "Italy",
            "Spain"
        ]
        
        # 预定义的国家显示范围（解决地图显示问题）
        self.country_extents = {
            'World': [-180, 180, -90, 90],
            'United States': [-130, -65, 20, 50],  # 美国本土
            'China': [70, 140, 15, 55],
            'Brazil': [-75, -30, -35, 10],
            'India': [65, 100, 5, 40],
            'Russia': [20, 180, 40, 85],
            'Canada': [-150, -50, 40, 85],
            'Australia': [110, 160, -45, -10],
            'New Zealand': [165, 180, -48, -34],
            'Japan': [125, 150, 25, 50],
            'United Kingdom': [-12, 5, 49, 62],
            'France': [-8, 10, 41, 52],
            'Germany': [5, 16, 47, 56],
            'Italy': [6, 19, 36, 48],
            'Spain': [-10, 5, 35, 45],
        }
        
        # 特殊国家（使用pr_文件夹）
        self.special_countries = ['New Zealand', 'United States']

    def display_countries_with_numbers(self):
        """显示带编号的国家列表"""
        print("\n" + "="*80)
        print("可用国家列表 (请记住编号)")
        print("="*80)
        
        # 分3列显示
        col_width = 25
        for i in range(0, len(self.countries), 3):
            line = ""
            for j in range(3):
                if i + j < len(self.countries):
                    item = f"{i+j+1:3d}. {self.countries[i+j]}"
                    line += f"{item:<{col_width}}"
            print(line)
        
        return self.countries

    def get_display_extent(self, country_name):
        """获取国家的显示范围"""
        return self.country_extents.get(country_name, [-180, 180, -90, 90])

    def load_rainfall_data(self, scenario, year, month, country_name):
        """加载降雨数据（简化版本）"""
        try:
            # 确定使用哪个文件夹
            if any(special in country_name for special in self.special_countries):
                folder = f"pr_{scenario}_202501-202812"
                prefix = "pr"
            else:
                folder = f"2pr_{scenario}_202501-202812"
                prefix = "2pr"
            
            tif_path = self.base_dir / folder / f"{prefix}_{year}{month:02d}.tif"
            
            if not tif_path.exists():
                raise FileNotFoundError(f"找不到数据文件: {tif_path}")
            
            print(f"找到数据文件: {tif_path}")
            return True, str(tif_path)
            
        except Exception as e:
            return False, str(e)

    def create_demo_visualization(self, country_name, scenario, year, month, output_dir):
        """创建演示可视化（不依赖复杂库）"""
        try:
            # 创建输出目录
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # 获取显示范围
            extent = self.get_display_extent(country_name)
            
            # 创建演示图
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # 创建演示数据
            lon_range = np.linspace(extent[0], extent[1], 100)
            lat_range = np.linspace(extent[2], extent[3], 80)
            lon_grid, lat_grid = np.meshgrid(lon_range, lat_range)
            
            # 生成演示降雨数据
            demo_data = np.random.rand(80, 100) * 200  # 0-200mm降雨
            
            # 绘制等值线图
            im = ax.contourf(lon_grid, lat_grid, demo_data, levels=20, 
                           cmap='viridis', extend='max')
            
            # 添加颜色条
            cbar = plt.colorbar(im, ax=ax, shrink=0.8, pad=0.05)
            cbar.set_label('Monthly Total Precipitation (mm)', fontsize=10)
            
            # 设置地图范围
            ax.set_xlim(extent[0], extent[1])
            ax.set_ylim(extent[2], extent[3])
            
            # 添加网格线
            ax.grid(True, alpha=0.3)
            ax.set_xlabel('Longitude')
            ax.set_ylabel('Latitude')
            
            # 设置标题
            title = f"{country_name} {scenario.upper()} {year}-{month:02d} Precipitation (DEMO)"
            ax.set_title(title, fontsize=14, pad=20)
            
            # 添加范围信息
            range_text = f"Display Range: [{extent[0]:.1f}, {extent[1]:.1f}, {extent[2]:.1f}, {extent[3]:.1f}]"
            ax.text(0.02, 0.98, range_text, transform=ax.transAxes, 
                   verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
            
            # 保存图片
            img_file = output_path / f"{country_name}_{scenario}_{year}{month:02d}_demo.png"
            plt.savefig(img_file, dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            plt.close()
            
            return True, str(img_file)
            
        except Exception as e:
            return False, str(e)

    def process_demo_task(self, scenario, year, month, country_name, output_dir):
        """处理演示任务"""
        try:
            print(f"处理: {country_name} {scenario} {year}-{month:02d}")
            
            # 检查数据文件
            data_ok, data_info = self.load_rainfall_data(scenario, year, month, country_name)
            if not data_ok:
                print(f"  数据文件问题: {data_info}")
                return False, None
            
            # 创建演示可视化
            viz_ok, img_file = self.create_demo_visualization(
                country_name, scenario, year, month, output_dir
            )
            
            if viz_ok:
                print(f"  ✅ 成功生成演示图: {img_file}")
                extent = self.get_display_extent(country_name)
                print(f"  显示范围: {extent}")
                return True, img_file
            else:
                print(f"  ❌ 可视化失败: {img_file}")
                return False, None
                
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
            return False, None

def parse_selection(input_str, max_num):
    """解析用户输入的选择"""
    selections = []
    
    if input_str.lower() == 'all':
        return list(range(1, max_num + 1))
    
    # 处理逗号分隔的选择
    parts = input_str.split(',')
    
    for part in parts:
        part = part.strip()
        if '-' in part:
            # 处理范围选择，如 1-5
            try:
                start, end = map(int, part.split('-'))
                selections.extend(range(start, end + 1))
            except:
                continue
        else:
            # 处理单个选择
            try:
                num = int(part)
                if 1 <= num <= max_num:
                    selections.append(num)
            except:
                continue
    
    return sorted(list(set(selections)))

def main():
    """主函数"""
    print("\n" + "="*80)
    print("🌧️  简化版CMIP6降雨数据可视化工具")
    print("专注解决: 1.编号选择国家  2.修复地图显示范围")
    print("="*80)
    
    try:
        # 初始化工具
        tool = SimpleCMIP6Tool()
        
        # 显示国家列表
        countries = tool.display_countries_with_numbers()
        
        # 选择国家
        while True:
            country_input = input(f"\n请输入国家编号 (1-{len(countries)}, 如: 1,2,3 或 all): ").strip()
            country_indices = parse_selection(country_input, len(countries))
            if country_indices:
                selected_countries = [countries[i-1] for i in country_indices]
                break
            print("输入无效，请重新输入")
        
        print(f"已选择国家: {', '.join(selected_countries)}")
        
        # 简单选择其他参数
        scenario = "ssp126"
        year = 2025
        month = 6
        output_dir = "demo_output"
        
        print(f"\n使用默认参数: {scenario}, {year}-{month:02d}")
        print(f"输出目录: {output_dir}")
        
        # 处理任务
        print(f"\n开始处理 {len(selected_countries)} 个任务...")
        
        success_count = 0
        for i, country in enumerate(selected_countries, 1):
            print(f"\n[{i}/{len(selected_countries)}] ", end="")
            success, img_file = tool.process_demo_task(scenario, year, month, country, output_dir)
            if success:
                success_count += 1
        
        print(f"\n" + "="*80)
        print(f"处理完成! 成功: {success_count}/{len(selected_countries)}")
        print(f"输出目录: {os.path.abspath(output_dir)}")
        print("="*80)
        
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
    except Exception as e:
        print(f"\n程序出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
