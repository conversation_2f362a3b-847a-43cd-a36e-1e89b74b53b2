#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CMIP6降雨数据可视化工具 - 启动器
快速启动GUI或命令行版本
"""

import sys
import os
from pathlib import Path

def check_dependencies():
    """检查依赖库"""
    required_packages = [
        'numpy', 'pandas', 'geopandas', 'matplotlib', 
        'cartopy', 'rasterio', 'shapely'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖库:")
        for pkg in missing_packages:
            print(f"   - {pkg}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖库已安装")
    return True

def check_data_files():
    """检查数据文件"""
    vertify_dir = Path(__file__).parent / "vertify"
    
    # 检查核心文件
    core_files = [
        vertify_dir / "extract_country_data.py",
        vertify_dir / "全球国家边界_按照国家分SHP" / "世界各国行政区划.shp"
    ]
    
    missing_files = []
    for file_path in core_files:
        if not file_path.exists():
            missing_files.append(str(file_path))
    
    # 检查数据文件夹
    data_folders = [
        "pr_ssp126_202501-202812", "2pr_ssp126_202501-202812",
        "pr_ssp245_202501-202812", "2pr_ssp245_202501-202812",
        "pr_ssp370_202501-202812", "2pr_ssp370_202501-202812", 
        "pr_ssp585_202501-202812", "2pr_ssp585_202501-202812"
    ]
    
    missing_folders = []
    for folder in data_folders:
        folder_path = vertify_dir / folder
        if not folder_path.exists():
            missing_folders.append(folder)
    
    if missing_files or missing_folders:
        print("❌ 缺少以下文件或文件夹:")
        for item in missing_files + missing_folders:
            print(f"   - {item}")
        return False
    
    print("✅ 数据文件检查通过")
    return True

def show_menu():
    """显示主菜单"""
    print("\n" + "="*60)
    print("🌧️  CMIP6降雨数据可视化工具")
    print("="*60)
    print("请选择启动方式:")
    print()
    print("1. 🖥️  GUI图形界面 (推荐新手)")
    print("2. 💻 命令行交互式")
    print("3. ⚡ 命令行快速模式")
    print("4. 📖 查看使用说明")
    print("5. 🔧 检查系统环境")
    print("6. ❌ 退出")
    print("="*60)

def launch_gui():
    """启动GUI版本"""
    try:
        print("正在启动GUI界面...")
        import cmip6_rainfall_visualizer
        cmip6_rainfall_visualizer.main()
    except ImportError as e:
        print(f"❌ 无法启动GUI: {e}")
        print("可能是tkinter库未安装，请尝试命令行版本")
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")

def launch_cli_interactive():
    """启动命令行交互式版本"""
    try:
        print("正在启动命令行交互式界面...")
        import cmip6_rainfall_cli
        sys.argv = ['cmip6_rainfall_cli.py', '--interactive']
        cmip6_rainfall_cli.main()
    except Exception as e:
        print(f"❌ 命令行启动失败: {e}")

def launch_cli_quick():
    """启动命令行快速模式"""
    print("\n快速模式示例:")
    print("1. 中国2025年夏季降雨 (SSP245)")
    print("2. 巴西全年降雨对比 (所有场景)")
    print("3. 全球2025年降雨 (SSP126)")
    print("4. 自定义参数")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == '1':
        cmd = [
            'python', 'cmip6_rainfall_cli.py',
            '--scenarios', 'ssp245',
            '--years', '2025', 
            '--months', '6,7,8',
            '--countries', 'China'
        ]
    elif choice == '2':
        cmd = [
            'python', 'cmip6_rainfall_cli.py',
            '--scenarios', 'ssp126,ssp245,ssp370,ssp585',
            '--years', '2025',
            '--months', 'all', 
            '--countries', 'Brazil'
        ]
    elif choice == '3':
        cmd = [
            'python', 'cmip6_rainfall_cli.py',
            '--scenarios', 'ssp126',
            '--years', '2025',
            '--months', 'all',
            '--countries', 'World'
        ]
    elif choice == '4':
        scenarios = input("场景 (如ssp126,ssp245): ").strip()
        years = input("年份 (如2025,2026): ").strip()
        months = input("月份 (如6,7,8或all): ").strip()
        countries = input("国家 (如China,Brazil): ").strip()
        
        cmd = [
            'python', 'cmip6_rainfall_cli.py',
            '--scenarios', scenarios,
            '--years', years,
            '--months', months,
            '--countries', countries
        ]
    else:
        print("无效选择")
        return
    
    print(f"\n执行命令: {' '.join(cmd)}")
    try:
        import subprocess
        subprocess.run(cmd)
    except Exception as e:
        print(f"❌ 执行失败: {e}")

def show_help():
    """显示帮助信息"""
    help_text = """
📖 CMIP6降雨数据可视化工具使用说明

🎯 主要功能:
- 多场景降雨数据可视化 (SSP126/245/370/585)
- 支持2025-2028年月度数据
- 自动生成专业地图和CSV数据
- 支持全球200+国家和地区

🚀 快速开始:
1. 选择GUI界面 - 适合新手，界面友好
2. 选择命令行 - 适合批量处理

📁 输出文件:
- PNG地图: 包含比例尺、网格线、国家边界
- CSV数据: 经纬度网格降雨量数据
- 汇总报告: 批处理结果统计

🌍 支持国家:
- 中国: China
- 美国: United States  
- 巴西: Brazil
- 印度: India
- 俄罗斯: Russia
- 日本: Japan
- 全球: World
- 等200+个国家...

💡 使用技巧:
- 大批量任务建议分批处理
- 选择合适的颜色方案提升可视化效果
- 使用季节快捷选择: summer(6-8月), winter(12,1,2月)

❓ 如需更多帮助，请查看 README_CMIP6_Rainfall_Visualizer.md
"""
    print(help_text)

def main():
    """主函数"""
    print("🌧️ CMIP6降雨数据可视化工具启动器")
    print("正在检查系统环境...")
    
    # 检查依赖和数据
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    if not check_data_files():
        input("\n按回车键退出...")
        return
    
    while True:
        show_menu()
        choice = input("\n请选择 (1-6): ").strip()
        
        if choice == '1':
            launch_gui()
        elif choice == '2':
            launch_cli_interactive()
        elif choice == '3':
            launch_cli_quick()
        elif choice == '4':
            show_help()
            input("\n按回车键继续...")
        elif choice == '5':
            print("\n🔧 重新检查系统环境...")
            check_dependencies()
            check_data_files()
            input("\n按回车键继续...")
        elif choice == '6':
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
