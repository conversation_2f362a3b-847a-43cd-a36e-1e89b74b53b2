#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CMIP6降雨数据可视化工具 - 启动器
快速启动GUI或命令行版本
"""

import sys
import os
from pathlib import Path

def check_dependencies():
    """检查依赖库"""
    # 基础依赖库（简化版本只需要这些）
    required_packages = [
        'numpy', 'pandas', 'matplotlib'
    ]

    # 可选依赖库（用于完整功能）
    optional_packages = [
        'geopandas', 'cartopy', 'rasterio', 'shapely'
    ]

    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print("❌ 缺少以下必需依赖库:")
        for pkg in missing_packages:
            print(f"   - {pkg}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False

    # 检查可选依赖
    missing_optional = []
    for package in optional_packages:
        try:
            __import__(package)
        except ImportError:
            missing_optional.append(package)

    if missing_optional:
        print("⚠️  缺少以下可选依赖库（将使用简化功能）:")
        for pkg in missing_optional:
            print(f"   - {pkg}")
        print("✅ 基础依赖库已安装，可以使用简化版本")
    else:
        print("✅ 所有依赖库已安装")

    return True

def check_data_files():
    """检查数据文件"""
    vertify_dir = Path(__file__).parent / "vertify"

    if not vertify_dir.exists():
        print("❌ vertify 数据目录不存在")
        return False

    # 检查核心shapefile（可选）
    shp_file = vertify_dir / "全球国家边界_按照国家分SHP" / "世界各国行政区划.shp"
    if not shp_file.exists():
        print("⚠️  国家边界shapefile不存在，将使用简化功能")

    # 检查数据文件夹
    data_folders = [
        "pr_ssp126_202501-202812", "2pr_ssp126_202501-202812",
        "pr_ssp245_202501-202812", "2pr_ssp245_202501-202812",
        "pr_ssp370_202501-202812", "2pr_ssp370_202501-202812",
        "pr_ssp585_202501-202812", "2pr_ssp585_202501-202812"
    ]

    existing_folders = []
    missing_folders = []

    for folder in data_folders:
        folder_path = vertify_dir / folder
        if folder_path.exists():
            existing_folders.append(folder)
        else:
            missing_folders.append(folder)

    if not existing_folders:
        print("❌ 没有找到任何CMIP6数据文件夹")
        print("请确保vertify目录下有以下文件夹之一:")
        for folder in data_folders:
            print(f"   - {folder}")
        return False

    print(f"✅ 找到 {len(existing_folders)} 个数据文件夹")
    if missing_folders:
        print(f"⚠️  缺少 {len(missing_folders)} 个数据文件夹（部分场景可能不可用）")

    return True

def show_menu():
    """显示主菜单"""
    print("\n" + "="*60)
    print("🌧️  CMIP6降雨数据可视化工具")
    print("="*60)
    print("请选择启动方式:")
    print()
    print("1. 🖥️  GUI图形界面 (推荐新手)")
    print("2. 💻 命令行交互式")
    print("3. ⚡ 命令行快速模式")
    print("4. 📖 查看使用说明")
    print("5. 🔧 检查系统环境")
    print("6. ❌ 退出")
    print("="*60)

def launch_gui():
    """启动GUI版本"""
    try:
        print("正在启动GUI界面...")
        import cmip6_rainfall_visualizer
        cmip6_rainfall_visualizer.main()
    except ImportError as e:
        print(f"❌ 无法启动GUI: {e}")
        print("可能是tkinter库未安装，请尝试命令行版本")
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")

def launch_cli_interactive():
    """启动命令行交互式版本"""
    try:
        print("正在启动命令行交互式界面...")

        # 首先尝试简化版本
        try:
            import cmip6_simple_cli
            sys.argv = ['cmip6_simple_cli.py', '--interactive']
            cmip6_simple_cli.main()
        except ImportError:
            # 如果简化版本不可用，尝试完整版本
            import cmip6_rainfall_cli
            sys.argv = ['cmip6_rainfall_cli.py', '--interactive']
            cmip6_rainfall_cli.main()

    except Exception as e:
        print(f"❌ 命令行启动失败: {e}")
        import traceback
        traceback.print_exc()

def launch_cli_quick():
    """启动命令行快速模式"""
    print("\n快速模式示例:")
    print("1. 中国2025年夏季降雨 (SSP245)")
    print("2. 巴西全年降雨对比 (所有场景)")
    print("3. 全球2025年降雨 (SSP126)")
    print("4. 自定义参数")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == '1':
        cmd = [
            'python', 'cmip6_simple_cli.py',
            '--scenarios', 'ssp245',
            '--years', '2025',
            '--months', '6,7,8',
            '--countries', 'China'
        ]
    elif choice == '2':
        cmd = [
            'python', 'cmip6_simple_cli.py',
            '--scenarios', 'ssp126,ssp245,ssp370,ssp585',
            '--years', '2025',
            '--months', 'all',
            '--countries', 'Brazil'
        ]
    elif choice == '3':
        cmd = [
            'python', 'cmip6_simple_cli.py',
            '--scenarios', 'ssp126',
            '--years', '2025',
            '--months', 'all',
            '--countries', 'World'
        ]
    elif choice == '4':
        scenarios = input("场景 (如ssp126,ssp245): ").strip()
        years = input("年份 (如2025,2026): ").strip()
        months = input("月份 (如6,7,8或all): ").strip()
        countries = input("国家 (如China,Brazil): ").strip()

        cmd = [
            'python', 'cmip6_simple_cli.py',
            '--scenarios', scenarios,
            '--years', years,
            '--months', months,
            '--countries', countries
        ]
    else:
        print("无效选择")
        return
    
    print(f"\n执行命令: {' '.join(cmd)}")
    try:
        import subprocess
        subprocess.run(cmd)
    except Exception as e:
        print(f"❌ 执行失败: {e}")

def show_help():
    """显示帮助信息"""
    help_text = """
📖 CMIP6降雨数据可视化工具使用说明

🎯 主要功能:
- 多场景降雨数据可视化 (SSP126/245/370/585)
- 支持2025-2028年月度数据
- 自动生成专业地图和CSV数据
- 支持全球200+国家和地区

🚀 快速开始:
1. 选择GUI界面 - 适合新手，界面友好
2. 选择命令行 - 适合批量处理

📁 输出文件:
- PNG地图: 包含比例尺、网格线、国家边界
- CSV数据: 经纬度网格降雨量数据
- 汇总报告: 批处理结果统计

🌍 支持国家:
- 中国: China
- 美国: United States  
- 巴西: Brazil
- 印度: India
- 俄罗斯: Russia
- 日本: Japan
- 全球: World
- 等200+个国家...

💡 使用技巧:
- 大批量任务建议分批处理
- 选择合适的颜色方案提升可视化效果
- 使用季节快捷选择: summer(6-8月), winter(12,1,2月)

❓ 如需更多帮助，请查看 README_CMIP6_Rainfall_Visualizer.md
"""
    print(help_text)

def main():
    """主函数"""
    print("🌧️ CMIP6降雨数据可视化工具启动器")
    print("正在检查系统环境...")
    
    # 检查依赖和数据
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    if not check_data_files():
        input("\n按回车键退出...")
        return
    
    while True:
        show_menu()
        choice = input("\n请选择 (1-6): ").strip()
        
        if choice == '1':
            launch_gui()
        elif choice == '2':
            launch_cli_interactive()
        elif choice == '3':
            launch_cli_quick()
        elif choice == '4':
            show_help()
            input("\n按回车键继续...")
        elif choice == '5':
            print("\n🔧 重新检查系统环境...")
            check_dependencies()
            check_data_files()
            input("\n按回车键继续...")
        elif choice == '6':
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
