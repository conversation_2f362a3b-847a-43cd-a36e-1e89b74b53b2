# CMIP6降雨数据可视化工具

这是一个用于处理和可视化CMIP6降雨数据的Python工具集，支持多场景、多年份、多月份、多国家的数据提取和地图可视化。

## 功能特点

- ✅ **多维度选择**: 支持多场景(SSP126/245/370/585)、多年份(2025-2028)、多月份(1-12)、多国家选择
- ✅ **智能坐标处理**: 自动处理0-360度和-180到180度坐标系统转换
- ✅ **专业地图可视化**: 包含比例尺、指北针、网格线、国家边界等专业地图元素
- ✅ **数据导出**: 自动导出CSV格式的网格数据，包含经纬度和降雨量信息
- ✅ **批量处理**: 支持大批量任务的自动化处理
- ✅ **多种界面**: 提供GUI图形界面和命令行界面两种使用方式

## 文件结构

```
├── cmip6_rainfall_visualizer.py    # GUI图形界面版本
├── cmip6_rainfall_cli.py           # 命令行界面版本
├── vertify/                        # 数据处理核心模块
│   ├── extract_country_data.py     # 核心数据提取类
│   ├── run_interactive.py          # 原始交互式程序
│   ├── pr_ssp126_202501-202812/    # 0-360度降雨数据(新西兰、美国)
│   ├── 2pr_ssp126_202501-202812/   # -180到180度降雨数据(其他国家)
│   ├── pr_ssp245_202501-202812/    # 其他场景数据...
│   ├── 2pr_ssp245_202501-202812/
│   └── 全球国家边界_按照国家分SHP/  # 国家边界Shapefile
└── extracted_data/                 # 默认输出目录
```

## 安装依赖

确保安装以下Python库：

```bash
pip install numpy pandas geopandas matplotlib cartopy rasterio shapely pathlib calendar
```

对于GUI版本，还需要：
```bash
pip install tkinter  # 通常Python自带
```

## 使用方法

### 方法1: GUI图形界面 (推荐)

```bash
python cmip6_rainfall_visualizer.py
```

GUI界面功能：
- 🎯 **场景选择**: 勾选需要的SSP场景
- 📅 **年份选择**: 勾选2025-2028年份
- 🗓️ **月份选择**: 支持单月、季节、全年选择
- 🌍 **国家选择**: 从列表中选择国家，支持多选
- 🎨 **颜色方案**: 选择地图配色方案
- 📁 **输出目录**: 指定结果保存位置
- 📊 **实时进度**: 显示处理进度和日志

### 方法2: 命令行交互式

```bash
python cmip6_rainfall_cli.py --interactive
```

交互式选择示例：
```
请选择场景: 1,3        # 选择SSP126和SSP370
请选择年份: 2025-2027  # 选择2025到2027年
请选择月份: summer     # 选择夏季月份(6-8月)
请选择国家: China,Brazil,World  # 选择中国、巴西和全球
```

### 方法3: 命令行参数

```bash
python cmip6_rainfall_cli.py \
    --scenarios ssp126,ssp245 \
    --years 2025,2026 \
    --months 6,7,8 \
    --countries China,Brazil \
    --color viridis \
    --output my_results
```

## 输入格式说明

### 场景选择
- `ssp126`: 低排放场景
- `ssp245`: 中等排放场景  
- `ssp370`: 高排放场景
- `ssp585`: 极高排放场景

### 时间选择
- **年份**: 2025, 2026, 2027, 2028
- **月份**: 1-12 (支持季节快捷选择: summer=6-8, winter=12,1,2)

### 国家选择
- 支持国家英文名称，如: `China`, `United States`, `Brazil`
- 特殊选项: `World` (全球数据)
- 支持模糊匹配，如输入`china`可匹配到`China`

### 颜色方案
- `viridis`: 蓝绿色渐变(默认)
- `plasma`: 紫红色渐变
- `coolwarm`: 蓝红色渐变
- `RdYlBu_r`: 红黄蓝反向
- `Spectral_r`: 光谱色反向
- `Blues`: 蓝色系
- `YlOrRd`: 黄橙红渐变

## 输出文件

处理完成后，在输出目录中会生成：

### 1. CSV数据文件
```
{国家}_{场景}_{年份}_{月份}_grid.csv
```
包含字段：
- `Date`: 日期 (YYYY-MM格式)
- `lon`: 经度
- `lat`: 纬度  
- `Rain`: 月总降雨量 (mm)
- `country`: 国家名称

### 2. 地图图片文件
```
{国家}_{场景}_{年份}{月份}.png
```
专业地图特点：
- 高分辨率(300 DPI)
- 包含比例尺(左下角)
- 包含网格线和坐标标注
- 国家边界突出显示
- 专业配色和图例

### 3. 批处理汇总
```
batch_processing_summary.csv
```
记录所有任务的处理状态和结果。

## 特殊国家处理

程序会根据国家位置自动选择合适的坐标系统：

- **新西兰、美国**: 使用`pr_`文件夹(0-360度数据)
- **其他国家**: 使用`2pr_`文件夹(-180到180度数据)

这样确保地图显示效果最佳，避免跨越180度经线的显示问题。

## 数据说明

- **原始数据**: CMIP6日降雨强度数据 (kg m-2 s-1)
- **处理后数据**: 月总降雨量 (mm) = 日强度 × 当月天数
- **空间分辨率**: 取决于原始CMIP6数据分辨率
- **时间范围**: 2025-2028年月度数据

## 故障排除

### 常见问题

1. **找不到国家**: 检查国家名称拼写，使用英文名称
2. **坐标显示异常**: 程序会自动处理，如有问题请检查TIF文件
3. **内存不足**: 减少同时处理的任务数量
4. **文件路径错误**: 确保vertify文件夹和数据文件存在

### 调试模式

在代码中启用调试信息：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 示例用法

### 快速开始
```bash
# 生成中国2025年夏季SSP245场景降雨地图
python cmip6_rainfall_cli.py \
    --scenarios ssp245 \
    --years 2025 \
    --months 6,7,8 \
    --countries China
```

### 批量对比
```bash
# 对比多个场景下巴西的年度降雨
python cmip6_rainfall_cli.py \
    --scenarios ssp126,ssp245,ssp370,ssp585 \
    --years 2025 \
    --months all \
    --countries Brazil
```

### 全球分析
```bash
# 生成全球2025-2028年所有场景数据
python cmip6_rainfall_cli.py \
    --scenarios all \
    --years 2025-2028 \
    --months all \
    --countries World
```

## 技术支持

如有问题或建议，请检查：
1. Python环境和依赖库版本
2. 数据文件完整性
3. 系统内存和磁盘空间
4. 文件路径权限

---

**开发信息**
- 版本: 1.0
- 开发者: AI Assistant  
- 更新日期: 2025-01-01
- Python版本要求: 3.7+
