import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import lightgbm as lgb
import xgboost as xgb
from sklearn.linear_model import LogisticRegression, LinearRegression
from sklearn.model_selection import KFold
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, ConfusionMatrixDisplay, roc_curve, auc, classification_report
from sklearn.ensemble import ExtraTreesRegressor
from catboost import CatBoostRegressor
import joblib
import os

# 深度学习模型相关导入
# from tensorflow.keras.models import Sequential
# from tensorflow.keras.layers import Dense, LSTM, Dropout
# from tensorflow.keras.callbacks import EarlyStopping
from sklearn.preprocessing import label_binarize
from itertools import cycle

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def create_features(df):
    """
    基于原始数据创建丰富的特征。
    """
    df_feat = df.copy()

    # --- 1. 时间特征 ---
    date_col = None
    for col in df.columns:
        if 'date' in col.lower() or '日期' in col or '时间' in col:
            try:
                df_feat[col] = pd.to_datetime(df_feat[col])
                date_col = col
                break
            except (ValueError, TypeError):
                continue
    
    if date_col:
        df_feat['month'] = df_feat[date_col].dt.month
        df_feat['dayofweek'] = df_feat[date_col].dt.dayofweek
        df_feat['dayofyear'] = df_feat[date_col].dt.dayofyear
    else:
        print("警告：未找到明确的日期列，将跳过时间特征工程。")

    # --- 2. 滚动与滞后特征 ---
    for window in [3, 7, 14]:
        df_feat[f'rain_roll_mean_{window}'] = df_feat['Rain'].rolling(window=window, min_periods=1).mean()
        df_feat[f'rain_roll_std_{window}'] = df_feat['Rain'].rolling(window=window, min_periods=1).std()
        df_feat[f'rain_roll_max_{window}'] = df_feat['Rain'].rolling(window=window, min_periods=1).max()
        
    for lag in range(1, 8):
        df_feat[f'spores_lag_{lag}'] = df_feat['Spores'].shift(lag)
        df_feat[f'rain_lag_{lag}'] = df_feat['Rain'].shift(lag)
        
    df_feat.fillna(0, inplace=True)
    return df_feat

def create_features_for_prediction(df):
    """
    只基于日期和降雨数据创建特征，不依赖历史孢子数据。
    适用于未来降雨预测场景。
    """
    df_feat = df.copy()

    # --- 1. 时间特征 ---
    date_col = None
    for col in df.columns:
        if 'date' in col.lower() or '日期' in col or '时间' in col:
            try:
                df_feat[col] = pd.to_datetime(df_feat[col])
                date_col = col
                break
            except (ValueError, TypeError):
                continue
    
    if date_col:
        df_feat['month'] = df_feat[date_col].dt.month
        df_feat['dayofweek'] = df_feat[date_col].dt.dayofweek
        df_feat['dayofyear'] = df_feat[date_col].dt.dayofyear
    else:
        print("警告：未找到明确的日期列，将跳过时间特征工程。")

    # --- 2. 只保留降雨相关的滚动与滞后特征 ---
    for window in [3, 7, 14]:
        df_feat[f'rain_roll_mean_{window}'] = df_feat['Rain'].rolling(window=window, min_periods=1).mean()
        df_feat[f'rain_roll_std_{window}'] = df_feat['Rain'].rolling(window=window, min_periods=1).std()
        df_feat[f'rain_roll_max_{window}'] = df_feat['Rain'].rolling(window=window, min_periods=1).max()
        
    for lag in range(1, 8):
        df_feat[f'rain_lag_{lag}'] = df_feat['Rain'].shift(lag)
        
    df_feat.fillna(0, inplace=True)
    return df_feat

def main():
    """主执行函数"""
    # --- 全局参数设置 ---
    OUTBREAK_THRESHOLDS = [1000, 100000]
    
    script_dir = os.path.dirname(os.path.abspath(__file__))
    # 为CatBoost指定训练日志目录，避免污染根目录
    catboost_train_dir = os.path.join(script_dir, 'catboost_info')
    os.makedirs(catboost_train_dir, exist_ok=True)
    
    results_path = os.path.join(script_dir, 'final_regression_results.png')
    data_file_path = "D:\\files\\枝干病害247\\to 刘凯歌\\GTD接种物数据to王慧\\GTD_data.xlsx"

    # --- 数据加载与目标定义 ---
    df = pd.read_excel(data_file_path)
    
    # 创建特征
    df_features = create_features(df)
    
    # 目标变量y: 第二天的孢子数 (进行log1p变换以平滑数据)
    df_features['target_spores'] = df_features['Spores'].shift(-1)
    df_features.dropna(subset=['target_spores'], inplace=True)
    
    # 用于评估的真实孢子数 (无变换)
    y_true_spores = df_features['target_spores'].copy()
    
    # 定义分类目标
    def classify_spores(spore_count):
        if spore_count <= OUTBREAK_THRESHOLDS[0]:
            return 0 # 低风险
        elif spore_count <= OUTBREAK_THRESHOLDS[1]:
            return 1 # 中风险
        else:
            return 2 # 高风险
    y_true_class = y_true_spores.apply(classify_spores)

    # 用于训练的log变换后的目标
    y_log = np.log1p(df_features['target_spores'])

    target_col = 'target_spores'
    features = [col for col in df_features.columns if col not in [target_col, 'Spores', 'Date', '日期', '时间']]
    
    X = df_features[features]

    # --- 采用时序交叉验证 (KFold, shuffle=False) ---
    # 数据集划分方式：5折交叉验证
    # - 每次使用约80%的数据作为训练集
    # - 约20%的数据作为验证集
    # - 每个数据点都会在某一轮次中作为验证集
    # - 没有单独的测试集，而是收集所有折次的验证集预测作为模型评估
    NFOLDS = 5
    folds = KFold(n_splits=NFOLDS, shuffle=False)
    
    oof_preds_lgb = np.zeros(len(X))
    oof_preds_xgb = np.zeros(len(X))
    oof_preds_cat = np.zeros(len(X))
    oof_preds_et = np.zeros(len(X))

    print(f"\n--- 开始 {NFOLDS}-折交叉验证 (四模型树集成) ---")
    
    for n_fold, (train_idx, valid_idx) in enumerate(folds.split(X)):
        print(f"\n--- 第 {n_fold + 1} 折 ---")
        
        X_train_fold, X_valid_fold = X.iloc[train_idx], X.iloc[valid_idx]
        y_log_train_fold, y_log_valid_fold = y_log.iloc[train_idx], y_log.iloc[valid_idx]
        y_true_class_train_fold, _ = y_true_class.iloc[train_idx], y_true_class.iloc[valid_idx]
        
        # 极限惩罚权重
        sample_weight_fold = np.ones(len(y_log_train_fold))
        sample_weight_fold[y_true_class_train_fold == 1] = 50 
        sample_weight_fold[y_true_class_train_fold == 2] = 25 

        # --- 训练四个树模型 ---
        lgb_model = lgb.LGBMRegressor(objective='regression_l1', n_estimators=5000, random_state=42, n_jobs=-1, verbosity=-1,
                                      learning_rate=0.02, max_depth=10, subsample=0.8, colsample_bytree=0.8, reg_lambda=0.1)
        lgb_model.fit(X_train_fold, y_log_train_fold, sample_weight=sample_weight_fold,
                      eval_set=[(X_valid_fold, y_log_valid_fold)], eval_metric='mae',
                      callbacks=[lgb.early_stopping(100, verbose=False)])
        oof_preds_lgb[valid_idx] = lgb_model.predict(X_valid_fold)

        xgb_model = xgb.XGBRegressor(objective='reg:squarederror', eval_metric='rmse', n_estimators=5000, use_label_encoder=False, random_state=42, n_jobs=-1,
                                     learning_rate=0.02, max_depth=10, subsample=0.8, colsample_bytree=0.8, reg_lambda=0.1,
                                     early_stopping_rounds=100)
        xgb_model.fit(X_train_fold, y_log_train_fold, sample_weight=sample_weight_fold,
                      eval_set=[(X_valid_fold, y_log_valid_fold)],
                      verbose=False)
        oof_preds_xgb[valid_idx] = xgb_model.predict(X_valid_fold)
        
        cat_model = CatBoostRegressor(iterations=5000, random_seed=42, verbose=0, loss_function='MAE', eval_metric='MAE',
                                      learning_rate=0.02, depth=10, rsm=0.8, subsample=0.8,
                                      train_dir=catboost_train_dir)
        cat_model.fit(X_train_fold, y_log_train_fold, sample_weight=sample_weight_fold,
                      eval_set=[(X_valid_fold, y_log_valid_fold)],
                      early_stopping_rounds=100)
        oof_preds_cat[valid_idx] = cat_model.predict(X_valid_fold)

        et_model = ExtraTreesRegressor(n_estimators=1500, random_state=42, n_jobs=-1, 
                                       max_depth=20, max_features=0.8)
        et_model.fit(X_train_fold, y_log_train_fold, sample_weight=sample_weight_fold)
        oof_preds_et[valid_idx] = et_model.predict(X_valid_fold)
        
    # --- 元模型训练与评估 ---
    print("\n--- 训练元模型并评估 ---")
    
    X_meta = np.vstack([oof_preds_lgb, oof_preds_xgb, oof_preds_cat, oof_preds_et]).T
    
    # 训练元回归模型 (用于1:1图)
    meta_model_reg = LinearRegression()
    meta_model_reg.fit(X_meta, y_log)
    final_preds_log = meta_model_reg.predict(X_meta)
    final_preds_spores = np.expm1(final_preds_log)
    final_preds_spores[final_preds_spores < 0] = 0
    final_preds_spores = pd.Series(final_preds_spores, index=y_true_spores.index)

    # "决策大脑"：使用带随机性的LGBMClassifier，以获得更稳健的结果
    meta_model_class = lgb.LGBMClassifier(
        objective='multiclass',
        random_state=42,
        class_weight='balanced',
        n_estimators=100, # 减少学习步骤
        learning_rate=0.05,
        colsample_bytree=0.6, # 增强特征随机性
        subsample=0.6, # 增强样本随机性
        max_depth=3 # 严格限制树的深度
    )
    meta_model_class.fit(X_meta, y_true_class)
    
    # 在全部数据上进行预测
    final_preds_proba = meta_model_class.predict_proba(X_meta)
    
    # --- 调整决策阈值以平衡精确率和召回率 ---
    # 默认使用概率最高的类别作为预测结果
    # 以下代码添加阈值调整，尤其针对中风险类别
    print("\n--- 调整决策阈值 ---")
    # 设置每个类别的概率阈值 [低风险阈值, 中风险阈值, 高风险阈值]
    # 中风险类别阈值设高一些，减少误报
    risk_thresholds = [0.4, 0.7, 0.6]  # 稍微降低中风险阈值
    
    # 应用自定义阈值的预测函数
    def custom_predict(probas, thresholds):
        # 首先获取原始的预测类别（概率最高的类别）
        raw_preds = np.argmax(probas, axis=1)
        # 对于预测为中风险(1)的样本，重新检查其概率是否超过阈值
        adjusted_preds = raw_preds.copy()
        for i in range(len(probas)):
            pred_class = raw_preds[i]
            # 如果预测的类别概率低于该类的阈值，则寻找下一个最高概率的类别
            if probas[i, pred_class] < thresholds[pred_class]:
                # 设置这个预测为0（低风险），因为我们不想过度预测风险
                adjusted_preds[i] = 0
        return adjusted_preds
    
    # 生成基于阈值调整的预测
    final_preds_class = custom_predict(final_preds_proba, risk_thresholds)
    final_preds_class = pd.Series(final_preds_class, index=y_true_class.index)
    
    # 尝试不同的阈值组合，找到最佳F1分数
    best_f1_macro = 0
    best_thresholds = risk_thresholds.copy()
    
    # 自动进行阈值网格搜索
    print("开始阈值网格搜索以优化中风险预测...")
    for t1 in np.arange(0.55, 0.8, 0.05):  # 中风险阈值范围调整
        for t2 in np.arange(0.5, 0.7, 0.05):  # 高风险阈值范围
            test_thresholds = [0.3, t1, t2]  # 低风险阈值较低
            test_preds = custom_predict(final_preds_proba, test_thresholds)
            test_preds_series = pd.Series(test_preds, index=y_true_class.index)
            
            # 评估指标
            f1 = f1_score(y_true_class, test_preds, average='macro', zero_division=0)
            prec = precision_score(y_true_class, test_preds, average=None, zero_division=0)
            
            # 重点关注中风险类别的指标
            if f1 > best_f1_macro and prec[1] >= 0.28:  # 稍微降低中风险精确率要求
                best_f1_macro = f1
                best_thresholds = test_thresholds.copy()
                print(f"更好的阈值: {test_thresholds}, F1: {f1:.4f}, 中风险精确率: {prec[1]:.4f}")
                
    print(f"最佳阈值组合: {best_thresholds}, 宏平均F1分数: {best_f1_macro:.4f}")
    final_preds_class = custom_predict(final_preds_proba, best_thresholds)
    final_preds_class = pd.Series(final_preds_class, index=y_true_class.index)
    
    # --- 分类评估 (基于全部OOF预测) ---
    print("\n--- 各类别详细分类指标 ---")
    # 注意: target_names的顺序必须与类别标签(0, 1, 2)对应
    unique_labels = sorted(y_true_class.unique())
    class_names_map = {0: '低风险', 1: '中风险', 2: '高风险'}
    target_names_for_report = [class_names_map[label] for label in unique_labels]
    print(classification_report(y_true_class, final_preds_class, labels=unique_labels, target_names=target_names_for_report, zero_division=0))

    print("\n--- 宏平均（Macro Average）分类指标 ---")
    print(f"准确度: {accuracy_score(y_true_class, final_preds_class):.4f}, "
          f"精确率 (Macro): {precision_score(y_true_class, final_preds_class, average='macro', zero_division=0):.4f}, "
          f"召回率 (Macro): {recall_score(y_true_class, final_preds_class, average='macro', zero_division=0):.4f}, "
          f"F1分数 (Macro): {f1_score(y_true_class, final_preds_class, average='macro', zero_division=0):.4f}")

    # --- 可视化最终模型 (基于全部OOF预测) ---
    print("\n--- 正在生成独立的评估图表... ---")
    
    unique_true_classes = np.sort(y_true_class.unique())
    all_class_labels = ['低风险', '中风险', '高风险']
    class_labels = [all_class_labels[i] for i in unique_true_classes]
    n_classes = len(unique_true_classes)
    y_true_bin = label_binarize(y_true_class, classes=unique_true_classes)
    
    thresholds = OUTBREAK_THRESHOLDS

    # 图1: 混淆矩阵
    plt.figure(figsize=(8, 6))
    cm = confusion_matrix(y_true_class, final_preds_class, labels=unique_true_classes)
    disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=class_labels)
    disp.plot(ax=plt.gca(), cmap=plt.cm.Blues)
    plt.title('混淆矩阵')
    plt.savefig(os.path.join(script_dir, '1_confusion_matrix.png'), dpi=300, bbox_inches='tight')
    plt.close()
    print("已保存: 1_confusion_matrix.png")

    # 图2: ROC 曲线
    plt.figure(figsize=(8, 6))
    colors = cycle(['navy', 'turquoise', 'darkorange'])
    for i, color in zip(range(n_classes), colors):
        if i < y_true_bin.shape[1] and unique_true_classes[i] < final_preds_proba.shape[1]:
            fpr, tpr, _ = roc_curve(y_true_bin[:, i], final_preds_proba[:, unique_true_classes[i]])
            roc_auc = auc(fpr, tpr)
            plt.plot(fpr, tpr, color=color, lw=2, label=f'{class_labels[i]} ROC (AUC = {roc_auc:.2f})')
    plt.plot([0, 1], [0, 1], 'r--')
    plt.xlabel('假正例率'); plt.ylabel('真正例率'); plt.title('ROC 曲线'); plt.legend(loc='lower right'); plt.grid(True)
    plt.savefig(os.path.join(script_dir, '2_roc_curve.png'), dpi=300, bbox_inches='tight')
    plt.close()
    print("已保存: 2_roc_curve.png")

    # # 图3: 预测值 vs. 真实值 (1:1 图)
    # plt.figure(figsize=(8, 6))
    # plt.scatter(y_true_spores, final_preds_spores, alpha=0.5, label='预测点')
    # lims = [np.min([y_true_spores.min(), final_preds_spores.min()]), np.max([y_true_spores.max(), final_preds_spores.max()])]
    # lims = [max(1, lims[0]), lims[1]]
    # plt.plot(lims, lims, 'r--', alpha=0.75, zorder=0, label='理想情况 (Y=X)')
    # plt.xscale('log'); plt.yscale('log'); plt.xlabel('真实孢子数 (对数刻度)'); plt.ylabel('预测孢子数 (对数刻度)')
    # plt.title('预测值 vs. 真实值'); plt.legend(); plt.grid(True, which="both", ls="--", linewidth=0.5)
    # plt.savefig(os.path.join(script_dir, '3_predicted_vs_actual.png'), dpi=300, bbox_inches='tight')
    # plt.close()
    # print("已保存: 3_predicted_vs_actual.png")

    # 图4: 风险等级预测时间线
    plt.figure(figsize=(15, 8))
    ax4 = plt.gca()
    time_index = y_true_spores.index
    ymax = y_true_spores.max() * 1.2
    ax4.axhspan(1, thresholds[0], facecolor='green', alpha=0.15, label='低风险区域')
    ax4.axhspan(thresholds[0], thresholds[1], facecolor='orange', alpha=0.15, label='中风险区域')
    ax4.axhspan(thresholds[1], ymax, facecolor='red', alpha=0.15, label='高风险区域')
    ax4.plot(time_index, y_true_spores, label='真实孢子数', color='black', alpha=0.7, zorder=3)
    plot_colors = {0: 'green', 1: 'orange', 2: 'red'}; plot_labels = {0: '低风险', 1: '中风险', 2: '高风险'}
    for level in unique_true_classes:
        color = plot_colors.get(level)
        true_indices = y_true_class[y_true_class == level].index
        if not true_indices.empty:
            points = y_true_spores.loc[true_indices]
            ax4.scatter(points.index, points, edgecolor=color, facecolors='none', s=120, marker='o', linewidth=1.5, label=f'真实-{plot_labels[level]}', zorder=5)
        pred_indices = final_preds_class[final_preds_class == level].index
        if not pred_indices.empty:
            points_on_true_line = y_true_spores.loc[pred_indices]
            ax4.scatter(points_on_true_line.index, points_on_true_line, color=color, s=100, marker='x', linewidth=2, label=f'预测-{plot_labels[level]}', zorder=6)
    ax4.set_title('风险等级预测时间线'); ax4.set_xlabel('样本索引'); ax4.set_ylabel('孢子数 (对数刻度)')
    ax4.set_yscale('log'); ax4.set_ylim(bottom=1, top=ymax); ax4.legend(); ax4.grid(True, which="both", ls="--", linewidth=0.5)
    plt.savefig(os.path.join(script_dir, '4_timeline.png'), dpi=300, bbox_inches='tight')
    plt.close()
    print("已保存: 4_timeline.png")

    # --- 最终环节: 重新训练完整模型并保存以备将来使用 ---
    print("\n--- 正在使用全部数据重新训练最终模型并保存... ---")
    
    # 1. 定义最终模型和特征要保存的路径
    final_model_path = os.path.join(script_dir, 'gtd_spore_prediction_model.joblib')
    
    # 2. 在全部数据上训练基础回归模型
    # 注意: 在最终训练中，我们不再需要验证集和early stopping，模型将使用全部数据进行学习
    sample_weight_full = np.ones(len(y_log))
    sample_weight_full[y_true_class == 1] = 50 
    sample_weight_full[y_true_class == 2] = 25

    # 训练最终的LGBM回归器 (使用优化的迭代次数，这里为示例，实际可设为CV中的平均最佳轮数)
    final_lgb = lgb.LGBMRegressor(objective='regression_l1', n_estimators=400, random_state=42, n_jobs=-1,
                                  learning_rate=0.02, max_depth=10, subsample=0.8, colsample_bytree=0.8, reg_lambda=0.1)
    final_lgb.fit(X, y_log, sample_weight=sample_weight_full)

    # 训练最终的XGBoost回归器
    final_xgb = xgb.XGBRegressor(objective='reg:squarederror', n_estimators=300, use_label_encoder=False, random_state=42, n_jobs=-1,
                                 learning_rate=0.02, max_depth=10, subsample=0.8, colsample_bytree=0.8, reg_lambda=0.1)
    final_xgb.fit(X, y_log, sample_weight=sample_weight_full)
    
    # 训练最终的CatBoost回归器
    final_cat = CatBoostRegressor(iterations=600, random_seed=42, verbose=0, loss_function='MAE',
                                  learning_rate=0.02, depth=10, rsm=0.8, subsample=0.8,
                                  train_dir=catboost_train_dir)
    final_cat.fit(X, y_log, sample_weight=sample_weight_full)
    
    # 训练最终的ExtraTrees回归器
    final_et = ExtraTreesRegressor(n_estimators=1500, random_state=42, n_jobs=-1, 
                                   max_depth=20, max_features=0.8)
    final_et.fit(X, y_log, sample_weight=sample_weight_full)
    
    # 3. 使用训练好的基础模型生成元特征
    meta_features_final = np.vstack([
        final_lgb.predict(X),
        final_xgb.predict(X),
        final_cat.predict(X),
        final_et.predict(X)
    ]).T
    
    # 4. 训练最终的元分类器
    final_meta_classifier = lgb.LGBMClassifier(
        objective='multiclass', random_state=42, class_weight='balanced',
        n_estimators=100, learning_rate=0.05,
        colsample_bytree=0.6, subsample=0.6, max_depth=3
    )
    final_meta_classifier.fit(meta_features_final, y_true_class)
    
    # 5. 将整个流水线打包并保存
    prediction_pipeline = {
        'base_models': [final_lgb, final_xgb, final_cat, final_et],
        'meta_model': final_meta_classifier,
        'feature_names': features # 保存特征列表至关重要
    }
    
    joblib.dump(prediction_pipeline, final_model_path)
    print(f"模型流水线已成功保存至: {final_model_path}")
    
    # --- 添加: 创建和保存一个只基于日期和降雨的迁移学习模型 ---
    create_rainfall_prediction_model(df, script_dir, catboost_train_dir, OUTBREAK_THRESHOLDS)

def create_rainfall_prediction_model(df, script_dir, catboost_train_dir, OUTBREAK_THRESHOLDS):
    """创建并保存一个只依赖日期和降雨的预测模型"""
    print("\n--- 训练迁移学习模型（只使用日期和降雨） ---")
    
    # 处理数据集，但这次只使用日期和降雨特征
    df_features_new = create_features_for_prediction(df)
    
    # 获取目标变量
    df_features_new['target_spores'] = df['Spores'].shift(-1)
    df_features_new.dropna(subset=['target_spores'], inplace=True)
    
    y_true_spores = df_features_new['target_spores'].copy()
    y_log = np.log1p(df_features_new['target_spores'])
    
    # 定义分类目标
    def classify_spores(spore_count):
        if spore_count <= OUTBREAK_THRESHOLDS[0]:
            return 0  # 低风险
        elif spore_count <= OUTBREAK_THRESHOLDS[1]:
            return 1  # 中风险
        else:
            return 2  # 高风险
    
    y_true_class = y_true_spores.apply(classify_spores)
    
    # 定义新特征集（无孢子滞后特征）
    transfer_features = [col for col in df_features_new.columns 
                       if col not in ['target_spores', 'Spores', 'Date', '日期', '时间'] 
                       and 'spores_lag' not in col.lower()]
    
    X_transfer = df_features_new[transfer_features]
    
    # 使用原始权重初始化新模型（迁移学习）
    sample_weight_full = np.ones(len(y_log))
    sample_weight_full[y_true_class == 1] = 50 
    sample_weight_full[y_true_class == 2] = 25
    
    # 训练最终的LGBM回归器
    final_lgb_transfer = lgb.LGBMRegressor(
        objective='regression_l1', n_estimators=400, random_state=42, n_jobs=-1,
        learning_rate=0.02, max_depth=10, subsample=0.8, colsample_bytree=0.8, reg_lambda=0.1)
    
    final_xgb_transfer = xgb.XGBRegressor(
        objective='reg:squarederror', n_estimators=300, use_label_encoder=False, 
        random_state=42, n_jobs=-1, learning_rate=0.02, max_depth=10, 
        subsample=0.8, colsample_bytree=0.8, reg_lambda=0.1)
    
    final_cat_transfer = CatBoostRegressor(
        iterations=600, random_seed=42, verbose=0, loss_function='MAE',
        learning_rate=0.02, depth=10, rsm=0.8, subsample=0.8,
        train_dir=catboost_train_dir)
    
    final_et_transfer = ExtraTreesRegressor(
        n_estimators=1500, random_state=42, n_jobs=-1, 
        max_depth=20, max_features=0.8)
    
    print("训练基础模型...")
    final_lgb_transfer.fit(X_transfer, y_log, sample_weight=sample_weight_full)
    final_xgb_transfer.fit(X_transfer, y_log, sample_weight=sample_weight_full)
    final_cat_transfer.fit(X_transfer, y_log, sample_weight=sample_weight_full)
    final_et_transfer.fit(X_transfer, y_log, sample_weight=sample_weight_full)
    
    # 生成元特征
    meta_features_transfer = np.vstack([
        final_lgb_transfer.predict(X_transfer),
        final_xgb_transfer.predict(X_transfer),
        final_cat_transfer.predict(X_transfer),
        final_et_transfer.predict(X_transfer)
    ]).T
    
    # 训练元模型
    print("训练元模型...")
    final_meta_classifier_transfer = lgb.LGBMClassifier(
        objective='multiclass', random_state=42, class_weight='balanced',
        n_estimators=100, learning_rate=0.05,
        colsample_bytree=0.6, subsample=0.6, max_depth=3
    )
    final_meta_classifier_transfer.fit(meta_features_transfer, y_true_class)
    
    # 构建并保存迁移学习模型流水线
    transfer_pipeline = {
        'base_models': [final_lgb_transfer, final_xgb_transfer, final_cat_transfer, final_et_transfer],
        'meta_model': final_meta_classifier_transfer,
        'feature_names': transfer_features
    }
    
    # 保存模型
    transfer_model_path = os.path.join(script_dir, 'gtd_rainfall_prediction_model.joblib')
    joblib.dump(transfer_pipeline, transfer_model_path)
    
    # 输出模型使用的特征
    print("\n--- 迁移学习模型使用的特征 ---")
    print("总特征数量:", len(transfer_features))
    print("特征列表:", transfer_features)
    
    print(f"迁移学习模型已成功保存至: {transfer_model_path}")

if __name__ == "__main__":
    main()