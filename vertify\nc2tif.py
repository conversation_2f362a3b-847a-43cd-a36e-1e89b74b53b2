import netCDF4 as nc
import numpy as np
from osgeo import gdal, osr, gdalconst
import os
import datetime

osr.UseExceptions()  # 适用于osr模块
gdal.UseExceptions() # 适用于gdal模块

# 用户输入起始和结束年月
start_year = 2025
start_month = 1
end_year = 2028
end_month = 12

# 选择气候情景
scenario = "ssp370"  # 可选: "ssp126", "ssp245", "ssp370", "ssp585"

# 使用绝对路径
current_dir = os.path.dirname(os.path.abspath(__file__))

# # 查找匹配的NetCDF文件
# scenario_files = {
#     "ssp126": "pr_Amon_NorESM2-MM_ssp126_r1i1p1f1_gn_202101-203012.nc",
#     "ssp245": "pr_Amon_NorESM2-MM_ssp245_r2i1p1f1_gn_202101-203012.nc",
#     "ssp370": "pr_Amon_NorESM2-MM_ssp370_r1i1p1f1_gn_202101-203012.nc",
#     "ssp585": "pr_Amon_NorESM2-MM_ssp585_r1i1p1f1_gn_202101-203012.nc"
# }
# 查找匹配的NetCDF文件
scenario_files = {
    "ssp126": "pr_Amon_MPI-ESM1-2-HR_ssp126_r1i1p1f1_gn_202501-202912.nc",
    "ssp245": "pr_Amon_MPI-ESM1-2-HR_ssp245_r1i1p1f1_gn_202501-202912.nc",
    "ssp370": "pr_Amon_MPI-ESM1-2-HR_ssp370_r1i1p1f1_gn_202501-202912.nc",
    "ssp585": "pr_Amon_MPI-ESM1-2-HR_ssp585_r1i1p1f1_gn_202501-202912.nc"
}

nc_file_name = scenario_files[scenario]
nc_file_path = os.path.join(current_dir, nc_file_name)

print(f"使用气候情景: {scenario}")
print(f"正在打开文件: {nc_file_path}")
print(f"文件是否存在: {os.path.exists(nc_file_path)}")

# 读取NetCDF文件
ds = nc.Dataset(nc_file_path)

# 提取变量和经纬度数据
pr_var = ds.variables['pr']
lon = ds.variables['lon'][:]
lat = ds.variables['lat'][:]
time_var = ds.variables['time']

# 将经度从0-360转换为-180到180，并排序
lon = np.where(lon > 180, lon - 360, lon)
sort_idx = np.argsort(lon)
lon = lon[sort_idx]

# 计算网格中心点的经纬度
lon_centers = lon - (lon[1] - lon[0]) / 2
lat_centers = lat - (lat[1] - lat[0]) / 2

# 确保经纬度保留两位小数
lon_centers = np.round(lon_centers, 2)
lat_centers = np.round(lat_centers, 2)

print(f"经度范围: {lon_centers.min()} 到 {lon_centers.max()}")
print(f"纬度范围: {lat_centers.min()} 到 {lat_centers.max()}")

# 确定时间维度
time_units = time_var.units
calendar = getattr(time_var, 'calendar', 'standard')

# 将输入的年月转换为NetCDF时间索引
def convert_year_month_to_index(year, month, time_var):
    """将年月转换为NetCDF文件中的时间索引"""
    # 假设NetCDF中的时间是从2025年1月开始的
    base_year = 2025
    base_month = 1
    
    # 计算月份差
    month_diff = (year - base_year) * 12 + (month - base_month)
    
    # 确保索引在有效范围内
    if month_diff < 0 or month_diff >= len(time_var):
        raise ValueError(f"时间范围超出NetCDF文件覆盖范围！有效范围: 2025年1月 - 2029年12月")
    
    return month_diff

# 计算起始和结束索引
start_idx = convert_year_month_to_index(start_year, start_month, time_var)
end_idx = convert_year_month_to_index(end_year, end_month, time_var)

print(f"处理时间范围: {start_year}年{start_month}月 至 {end_year}年{end_month}月")
print(f"对应NetCDF索引: {start_idx} - {end_idx}")

# 打印数据值范围，帮助调试
pr_sample = pr_var[0,:,:]
print(f"降水数据范围: 最小值={np.nanmin(pr_sample)}, 最大值={np.nanmax(pr_sample)}")

# 定义WGS84投影
srs = osr.SpatialReference()
srs.ImportFromEPSG(4326)
proj_wkt = srs.ExportToWkt()

# 计算分辨率（基于网格中心点）
lon_res = np.unique(np.round(np.diff(lon), 3))[0]   # 取唯一的 Δlon
lat_res = np.unique(np.round(np.diff(lat), 3))[0]

# 创建输出目录
output_dir = os.path.join(current_dir, f"2pr_{scenario}_{start_year}{start_month:02d}-{end_year}{end_month:02d}")
os.makedirs(output_dir, exist_ok=True)

# 遍历所有指定的月份
for idx in range(start_idx, end_idx + 1):
    # 计算当前年月
    current_month = ((start_month - 1) + idx - start_idx) % 12 + 1
    current_year = start_year + ((start_month - 1) + idx - start_idx) // 12
    
    # 提取当前月份的数据
    pr_data = pr_var[idx, :, :].copy()
    # 按经度重排数据
    pr_data = pr_data[:, sort_idx]
    
    # 数据缩放，使其在可视化时更明显
    # 降水数据通常以kg/m²/s为单位，转换为mm/day会更直观
    scale_factor = 86400  # 24小时 * 60分钟 * 60秒
    pr_data = pr_data * scale_factor
    
    output_tif = os.path.join(output_dir, f'2pr_{current_year}{current_month:02d}.tif')

    driver = gdal.GetDriverByName('GTiff')
    out_tif = driver.Create(
        output_tif,
        pr_data.shape[1],
        pr_data.shape[0],
        1,
        gdal.GDT_Float32
    )

    # 使用网格中心点计算地理变换
    # 左上角经度，经度分辨率，旋转，左上角纬度，旋转，纬度分辨率(负值)
    geotransform = [
        lon_centers.min() - lon_res/2,  # 左上角经度
        lon_res,                        # 经度分辨率
        0,                              # 旋转
        lat_centers.max() + lat_res/2,  # 左上角纬度
        0,                              # 旋转
        -abs(lat_res)                   # 纬度分辨率(负值)
    ]
    
    out_tif.SetGeoTransform(geotransform)
    out_tif.SetProjection(proj_wkt)

    # 写入数据并保存
    out_tif.GetRasterBand(1).WriteArray(pr_data)
    out_tif.GetRasterBand(1).SetNoDataValue(-9999)  # 设置一个明确的NoData值
    
    # 设置颜色表以增强可视化
    stats = out_tif.GetRasterBand(1).GetStatistics(0, 1)
    out_tif.GetRasterBand(1).SetStatistics(stats[0], stats[1], stats[2], stats[3])
    
    out_tif.FlushCache()
    out_tif = None

    print(f"保存文件 {output_tif} ({current_year}年{current_month}月)")
    print(f"网格中心点经度: {lon_centers[0]} 到 {lon_centers[-1]}")
    print(f"网格中心点纬度: {lat_centers[0]} 到 {lat_centers[-1]}")
print("原始 NetCDF 维度：", pr_var.shape)
print("Δlon =", np.unique(np.round(np.diff(lon), 4))[0], "°")
print("Δlat =", np.unique(np.round(np.diff(lat), 4))[0], "°")
# 关闭NetCDF文件
ds.close()

print(f"\n处理完成！共处理了 {end_idx - start_idx + 1} 个月的数据")
print(f"输出文件保存在: {output_dir}")
print(f"所有经纬度已保留两位小数，基于网格中心点计算")
