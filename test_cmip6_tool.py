#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CMIP6降雨数据可视化工具 - 测试脚本
用于验证程序功能是否正常
"""

import os
import sys
from pathlib import Path

# 添加vertify目录到路径
vertify_dir = Path(__file__).parent / "vertify"
if str(vertify_dir) not in sys.path:
    sys.path.insert(0, str(vertify_dir))

def test_imports():
    """测试导入功能"""
    print("🧪 测试1: 检查依赖库导入...")
    
    try:
        import numpy as np
        import pandas as pd
        import geopandas as gpd
        import matplotlib.pyplot as plt
        import cartopy.crs as ccrs
        import rasterio
        print("✅ 所有依赖库导入成功")
        return True
    except ImportError as e:
        print(f"❌ 依赖库导入失败: {e}")
        return False

def test_data_extractor():
    """测试数据提取器初始化"""
    print("\n🧪 测试2: 初始化数据提取器...")
    
    try:
        from extract_country_data import CountryRainfallExtractor
        
        tif_dir = vertify_dir
        shp_dir = vertify_dir / '全球国家边界_按照国家分SHP'
        
        if not shp_dir.exists():
            print(f"❌ Shapefile目录不存在: {shp_dir}")
            return False
        
        extractor = CountryRainfallExtractor(str(tif_dir), str(shp_dir))
        countries = extractor.get_available_countries()
        
        print(f"✅ 数据提取器初始化成功")
        print(f"   加载了 {len(countries)} 个国家")
        print(f"   前10个国家: {countries[:10]}")
        
        return True, extractor
        
    except Exception as e:
        print(f"❌ 数据提取器初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_data_files():
    """测试数据文件存在性"""
    print("\n🧪 测试3: 检查数据文件...")
    
    # 检查场景文件夹
    scenarios = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
    missing_folders = []
    
    for scenario in scenarios:
        folder1 = vertify_dir / f"pr_{scenario}_202501-202812"
        folder2 = vertify_dir / f"2pr_{scenario}_202501-202812"
        
        if not folder1.exists():
            missing_folders.append(str(folder1))
        if not folder2.exists():
            missing_folders.append(str(folder2))
    
    if missing_folders:
        print("❌ 缺少以下数据文件夹:")
        for folder in missing_folders:
            print(f"   - {folder}")
        return False
    
    # 检查示例TIF文件
    test_file = vertify_dir / "pr_ssp126_202501-202812" / "pr_202501.tif"
    if not test_file.exists():
        print(f"❌ 示例TIF文件不存在: {test_file}")
        return False
    
    print("✅ 数据文件检查通过")
    return True

def test_small_extraction():
    """测试小规模数据提取"""
    print("\n🧪 测试4: 小规模数据提取测试...")
    
    try:
        from extract_country_data import CountryRainfallExtractor
        
        tif_dir = vertify_dir
        shp_dir = vertify_dir / '全球国家边界_按照国家分SHP'
        extractor = CountryRainfallExtractor(str(tif_dir), str(shp_dir))
        
        # 测试提取中国2025年1月数据
        print("   正在提取中国2025年1月SSP126数据...")
        df, csv_file, error = extractor.extract_data(
            scenario='ssp126',
            year=2025,
            month=1,
            country_name='China',
            output_dir='test_output'
        )
        
        if error:
            print(f"❌ 数据提取失败: {error}")
            return False
        
        if df is None or df.empty:
            print("❌ 提取的数据为空")
            return False
        
        print(f"✅ 数据提取成功")
        print(f"   数据点数: {len(df)}")
        print(f"   降雨量范围: {df['Rain'].min():.2f} - {df['Rain'].max():.2f} mm")
        print(f"   CSV文件: {csv_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据提取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_visualization():
    """测试可视化功能"""
    print("\n🧪 测试5: 可视化功能测试...")
    
    try:
        from extract_country_data import CountryRainfallExtractor
        
        tif_dir = vertify_dir
        shp_dir = vertify_dir / '全球国家边界_按照国家分SHP'
        extractor = CountryRainfallExtractor(str(tif_dir), str(shp_dir))
        
        # 测试生成中国地图
        print("   正在生成中国2025年1月降雨地图...")
        
        # 先提取数据
        df, csv_file, error = extractor.extract_data(
            scenario='ssp126',
            year=2025,
            month=1,
            country_name='China',
            output_dir='test_output'
        )
        
        if error or df is None:
            print(f"❌ 数据提取失败，无法测试可视化")
            return False
        
        # 生成地图
        img_path = extractor.plot_heatmap_precise(
            df=df,
            scenario='ssp126',
            year=2025,
            month=1,
            country='China',
            out_dir='test_output',
            color_scheme='viridis'
        )
        
        if img_path and Path(img_path).exists():
            print(f"✅ 地图生成成功")
            print(f"   图片文件: {img_path}")
            return True
        else:
            print("❌ 地图生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 可视化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始CMIP6降雨数据可视化工具测试")
    print("="*60)
    
    tests = [
        ("依赖库导入", test_imports),
        ("数据文件检查", test_data_files),
        ("数据提取器初始化", test_data_extractor),
        ("小规模数据提取", test_small_extraction),
        ("可视化功能", test_visualization)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            if test_name == "数据提取器初始化":
                result = test_func()
                if isinstance(result, tuple):
                    success = result[0]
                else:
                    success = result
            else:
                success = test_func()
            
            results.append((test_name, success))
            
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 执行异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*60)
    print("🏁 测试结果汇总")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if success:
            passed += 1
    
    print("-"*60)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过! 程序可以正常使用")
        print("\n💡 建议:")
        print("   - 运行 python start_cmip6_tool.py 启动主程序")
        print("   - 或运行 python cmip6_rainfall_visualizer.py 启动GUI")
    else:
        print("⚠️  部分测试失败，请检查环境配置")
        print("\n🔧 故障排除:")
        print("   - 检查Python依赖库是否完整安装")
        print("   - 确认vertify文件夹中的数据文件完整")
        print("   - 查看详细错误信息进行调试")
    
    return passed == total

def main():
    """主函数"""
    try:
        success = run_all_tests()
        
        if success:
            print(f"\n🚀 测试完成! 可以开始使用工具了")
            
            # 询问是否启动主程序
            choice = input("\n是否现在启动主程序? (y/n): ").strip().lower()
            if choice == 'y':
                try:
                    import start_cmip6_tool
                    start_cmip6_tool.main()
                except Exception as e:
                    print(f"启动主程序失败: {e}")
        else:
            print(f"\n❌ 测试未完全通过，请先解决问题")
            
    except KeyboardInterrupt:
        print(f"\n\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
