#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CMIP6降雨数据可视化工具 - 简化版本
支持多场景、多年份、多月份、多国家的数据提取和可视化
修复地图显示范围问题，使用编号选择国家
"""

import numpy as np
import pandas as pd
import geopandas as gpd
import matplotlib.pyplot as plt
import matplotlib.colors as colors
import cartopy.crs as ccrs
import cartopy.feature as cfeature
from cartopy.mpl.gridliner import LONGITUDE_FORMATTER, LATITUDE_FORMATTER
import rasterio
from shapely.ops import unary_union
import pathlib
import calendar
import os
from pathlib import Path

class CMIP6RainfallTool:
    def __init__(self):
        self.base_dir = Path(__file__).parent / "vertify"
        self.shp_path = self.base_dir / "全球国家边界_按照国家分SHP" / "世界各国行政区划.shp"
        
        # 加载国家边界数据
        if not self.shp_path.exists():
            raise FileNotFoundError(f"找不到shapefile: {self.shp_path}")
        
        self.world_gdf = gpd.read_file(self.shp_path)
        self.country_name_col = 'FENAME'
        
        # 场景配置
        self.scenarios = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
        self.years = [2025, 2026, 2027, 2028]
        self.months = list(range(1, 13))
        
        # 特殊国家（使用pr_文件夹，0-360度坐标）
        self.special_countries = ['New Zealand', 'United States']

    def get_available_countries(self):
        """获取可用国家列表"""
        countries = sorted(self.world_gdf[self.country_name_col].dropna().unique())
        return countries

    def display_countries_with_numbers(self):
        """显示带编号的国家列表"""
        countries = self.get_available_countries()
        display_list = ["World (全球)"] + countries
        
        print("\n" + "="*80)
        print("可用国家列表 (请记住编号)")
        print("="*80)
        
        # 分3列显示
        col_width = 25
        for i in range(0, len(display_list), 3):
            line = ""
            for j in range(3):
                if i + j < len(display_list):
                    item = f"{i+j+1:3d}. {display_list[i+j]}"
                    line += f"{item:<{col_width}}"
            print(line)
        
        return display_list

    def get_country_bounds(self, country_name):
        """获取国家边界"""
        if country_name == "World":
            return None
        
        # 查找国家
        country_gdf = self.world_gdf[self.world_gdf[self.country_name_col].str.upper() == country_name.upper()]
        
        if country_gdf.empty:
            # 模糊匹配
            for name in self.world_gdf[self.country_name_col].dropna():
                if country_name.lower() in name.lower():
                    country_gdf = self.world_gdf[self.world_gdf[self.country_name_col] == name]
                    break
        
        if country_gdf.empty:
            raise ValueError(f"找不到国家: {country_name}")
        
        return unary_union(country_gdf.geometry.buffer(0))

    def calculate_display_extent(self, country_name, geom):
        """计算合适的地图显示范围"""
        if country_name == "World":
            return [-180, 180, -90, 90]
        
        # 预定义的国家范围（解决显示问题）
        predefined_extents = {
            'United States': [-130, -65, 20, 50],
            'China': [70, 140, 15, 55],
            'Brazil': [-75, -30, -35, 10],
            'India': [65, 100, 5, 40],
            'Russia': [20, 180, 40, 85],
            'Canada': [-150, -50, 40, 85],
            'Australia': [110, 160, -45, -10],
            'New Zealand': [165, 180, -48, -34],
            'Japan': [125, 150, 25, 50],
            'United Kingdom': [-12, 5, 49, 62],
            'France': [-8, 10, 41, 52],
            'Germany': [5, 16, 47, 56],
        }
        
        if country_name in predefined_extents:
            return predefined_extents[country_name]
        
        # 自动计算范围
        bounds = geom.bounds
        minx, miny, maxx, maxy = bounds
        
        # 计算中心点和范围
        center_x = (minx + maxx) / 2
        center_y = (miny + maxy) / 2
        width = maxx - minx
        height = maxy - miny
        
        # 确保最小范围
        min_size = 3.0
        if width < min_size:
            width = min_size
        if height < min_size:
            height = min_size
        
        # 添加20%缓冲
        buffer = 0.2
        width *= (1 + buffer)
        height *= (1 + buffer)
        
        extent = [
            center_x - width/2,
            center_x + width/2,
            center_y - height/2,
            center_y + height/2
        ]
        
        # 限制在有效范围内
        extent[0] = max(extent[0], -180)
        extent[1] = min(extent[1], 180)
        extent[2] = max(extent[2], -90)
        extent[3] = min(extent[3], 90)
        
        return extent

    def load_rainfall_data(self, scenario, year, month, country_name):
        """加载降雨数据"""
        # 确定使用哪个文件夹
        if any(special in country_name for special in self.special_countries):
            folder = f"pr_{scenario}_202501-202812"
            prefix = "pr"
        else:
            folder = f"2pr_{scenario}_202501-202812"
            prefix = "2pr"
        
        tif_path = self.base_dir / folder / f"{prefix}_{year}{month:02d}.tif"
        
        if not tif_path.exists():
            raise FileNotFoundError(f"找不到数据文件: {tif_path}")
        
        # 读取数据
        with rasterio.open(tif_path) as src:
            data = src.read(1).astype('float32')
            transform = src.transform
            
            # 转换为月总降雨量
            days_in_month = calendar.monthrange(year, month)[1]
            data = data * days_in_month
            
            return data, transform

    def create_coordinate_grids(self, data, transform):
        """创建坐标网格"""
        height, width = data.shape
        cols = np.arange(width)
        rows = np.arange(height)
        
        # 计算经纬度
        lons = []
        lats = []
        for col in cols:
            x, _ = rasterio.transform.xy(transform, 0, col, offset='center')
            lons.append(x)
        for row in rows:
            _, y = rasterio.transform.xy(transform, row, 0, offset='center')
            lats.append(y)
        
        lon_grid, lat_grid = np.meshgrid(lons, lats)
        return lon_grid, lat_grid

    def mask_data_by_country(self, data, transform, geom):
        """根据国家边界裁剪数据"""
        from rasterio.features import geometry_mask
        from shapely.geometry import mapping
        
        try:
            mask = geometry_mask([mapping(geom)], transform=transform, 
                               invert=True, out_shape=data.shape)
            return np.where(mask, data, np.nan)
        except:
            return data

    def create_visualization(self, data, lon_grid, lat_grid, country_name, 
                           scenario, year, month, extent, output_dir):
        """创建可视化地图"""
        # 设置图形
        fig = plt.figure(figsize=(12, 8))
        ax = plt.axes(projection=ccrs.PlateCarree())
        
        # 设置地图范围
        ax.set_extent(extent, crs=ccrs.PlateCarree())
        
        # 添加地图要素
        ax.add_feature(cfeature.COASTLINE, linewidth=0.5)
        ax.add_feature(cfeature.BORDERS, linewidth=0.3)
        ax.add_feature(cfeature.OCEAN, color='lightblue', alpha=0.3)
        ax.add_feature(cfeature.LAND, color='lightgray', alpha=0.3)
        
        # 绘制降雨数据
        valid_data = ~np.isnan(data)
        if np.any(valid_data):
            vmin, vmax = np.nanmin(data), np.nanmax(data)
            
            # 绘制等值线图
            im = ax.contourf(lon_grid, lat_grid, data, levels=50, 
                           cmap='viridis', transform=ccrs.PlateCarree(),
                           vmin=vmin, vmax=vmax, extend='max')
            
            # 添加颜色条
            cbar = plt.colorbar(im, ax=ax, shrink=0.8, pad=0.05)
            cbar.set_label('Monthly Total Precipitation (mm)', fontsize=10)
        
        # 添加网格线
        gl = ax.gridlines(draw_labels=True, linewidth=0.5, alpha=0.5)
        gl.top_labels = False
        gl.right_labels = False
        
        # 添加比例尺（左下角）
        self.add_scale_bar(ax, extent)
        
        # 添加指北针（右上角）
        self.add_north_arrow(ax, extent)
        
        # 设置标题
        title = f"{country_name} {scenario.upper()} {year}-{month:02d} Precipitation"
        plt.title(title, fontsize=14, pad=20)
        
        # 保存图片
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        img_file = output_path / f"{country_name}_{scenario}_{year}{month:02d}.png"
        plt.savefig(img_file, dpi=300, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        plt.close()
        
        return str(img_file)

    def add_scale_bar(self, ax, extent):
        """添加比例尺"""
        try:
            from cartopy.mpl.gridliner import LONGITUDE_FORMATTER, LATITUDE_FORMATTER
            
            # 计算比例尺位置（左下角）
            x_range = extent[1] - extent[0]
            y_range = extent[3] - extent[2]
            
            scale_x = extent[0] + x_range * 0.05
            scale_y = extent[2] + y_range * 0.1
            
            # 计算比例尺长度
            scale_length = min(x_range * 0.2, 5.0)  # 最大5度
            
            # 绘制比例尺
            ax.plot([scale_x, scale_x + scale_length], [scale_y, scale_y], 
                   'k-', linewidth=3, transform=ccrs.PlateCarree())
            
            # 添加标签
            ax.text(scale_x + scale_length/2, scale_y - y_range*0.03, 
                   f'{scale_length:.0f}°', ha='center', va='top',
                   transform=ccrs.PlateCarree(), fontsize=10)
        except:
            pass

    def add_north_arrow(self, ax, extent):
        """添加指北针"""
        try:
            # 计算指北针位置（右上角）
            x_range = extent[1] - extent[0]
            y_range = extent[3] - extent[2]
            
            arrow_x = extent[1] - x_range * 0.08
            arrow_y = extent[3] - y_range * 0.12
            
            # 绘制箭头
            ax.annotate('', xy=(arrow_x, arrow_y + y_range*0.05), 
                       xytext=(arrow_x, arrow_y),
                       arrowprops=dict(arrowstyle='->', lw=2, color='black'),
                       transform=ccrs.PlateCarree())
            
            # 添加N标签
            ax.text(arrow_x, arrow_y + y_range*0.07, 'N',
                   ha='center', va='center', fontsize=12, fontweight='bold',
                   transform=ccrs.PlateCarree())
        except:
            pass

    def export_csv_data(self, data, lon_grid, lat_grid, country_name, 
                       scenario, year, month, output_dir):
        """导出CSV数据"""
        # 获取有效数据点
        valid_mask = ~np.isnan(data)
        
        if not np.any(valid_mask):
            return None
        
        # 提取有效数据
        valid_lons = lon_grid[valid_mask]
        valid_lats = lat_grid[valid_mask]
        valid_rain = data[valid_mask]
        
        # 创建DataFrame
        df = pd.DataFrame({
            'Date': f"{year}-{month:02d}",
            'lon': valid_lons,
            'lat': valid_lats,
            'Rain': valid_rain,
            'country': country_name
        })
        
        # 保存CSV
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        csv_file = output_path / f"{country_name}_{scenario}_{year}_{month:02d}_grid.csv"
        df.to_csv(csv_file, index=False)
        
        return str(csv_file)

    def process_single_task(self, scenario, year, month, country_name, output_dir):
        """处理单个任务"""
        try:
            print(f"处理: {country_name} {scenario} {year}-{month:02d}")
            
            # 加载数据
            data, transform = self.load_rainfall_data(scenario, year, month, country_name)
            
            # 创建坐标网格
            lon_grid, lat_grid = self.create_coordinate_grids(data, transform)
            
            # 处理国家边界
            if country_name != "World":
                geom = self.get_country_bounds(country_name)
                data = self.mask_data_by_country(data, transform, geom)
                extent = self.calculate_display_extent(country_name, geom)
            else:
                extent = [-180, 180, -90, 90]
            
            # 创建可视化
            img_file = self.create_visualization(data, lon_grid, lat_grid, 
                                               country_name, scenario, year, month, 
                                               extent, output_dir)
            
            # 导出CSV数据
            csv_file = self.export_csv_data(data, lon_grid, lat_grid, 
                                          country_name, scenario, year, month, 
                                          output_dir)
            
            return True, img_file, csv_file
            
        except Exception as e:
            print(f"错误: {e}")
            return False, None, None
