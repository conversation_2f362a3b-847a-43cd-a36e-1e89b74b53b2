#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CMIP6降雨数据可视化工具 - 简化版本
实现核心功能：
1. 编号选择国家
2. 国家形状匹配TIF数据
3. 乘以当月天数转换为月总降雨量
4. 热图可视化
5. 比例尺、指北针等地图要素
"""

import os
import sys
from pathlib import Path
import argparse
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import matplotlib.colors as colors
from matplotlib.patches import Rectangle
import calendar

class SimpleCMIP6Tool:
    """简化版CMIP6降雨数据处理工具"""
    
    def __init__(self):
        """初始化工具"""
        self.base_dir = Path(__file__).parent / "vertify"
        
        # 预定义国家列表（简化版本）
        self.countries = [
            "China", 
            "United States",
            "Brazil", 
            "India",
            "Russia",
            "Canada",
            "Australia", 
            "New Zealand",
            "Japan",
            "United Kingdom",
            "France",
            "Germany",
            "Italy",
            "Spain",
            "Mexico",
            "Argentina",
            "South Africa",
            "Egypt",
            "Nigeria",
            "Kenya"
        ]
        
        # 场景配置
        self.scenarios = {
            "ssp126": "pr_ssp126_202501-202812", 
            "ssp245": "pr_ssp245_202501-202812",
            "ssp370": "pr_ssp370_202501-202812", 
            "ssp585": "pr_ssp585_202501-202812"
        }
        
        # 2pr文件夹（-180到180度坐标）
        self.scenarios_2pr = {
            "ssp126": "2pr_ssp126_202501-202812", 
            "ssp245": "2pr_ssp245_202501-202812",
            "ssp370": "2pr_ssp370_202501-202812", 
            "ssp585": "2pr_ssp585_202501-202812"
        }
        
        # 需要使用pr文件夹的特殊国家（0-360度坐标）
        self.special_countries = ['New Zealand', 'United States']
        
        # 预定义的国家显示范围（修复地图显示问题）
        self.country_extents = {
            'United States': [-130, -65, 20, 50],  # 美国本土
            'China': [70, 140, 15, 55],
            'Brazil': [-75, -30, -35, 10],
            'India': [65, 100, 5, 40],
            'Russia': [20, 180, 40, 85],
            'Canada': [-150, -50, 40, 85],
            'Australia': [110, 160, -45, -10],
            'New Zealand': [165, 180, -48, -34],
            'Japan': [125, 150, 25, 50],
            'United Kingdom': [-12, 5, 49, 62],
            'France': [-8, 10, 41, 52],
            'Germany': [5, 16, 47, 56],
            'Italy': [6, 19, 36, 48],
            'Spain': [-10, 5, 35, 45],
            'Mexico': [-120, -85, 14, 33],
            'Argentina': [-75, -53, -55, -22],
            'South Africa': [16, 33, -35, -22],
            'Egypt': [25, 37, 22, 32],
            'Nigeria': [3, 15, 4, 14],
            'Kenya': [34, 42, -5, 5],
        }

    def get_available_countries(self):
        """获取可用国家列表"""
        return self.countries

    def display_countries_with_numbers(self):
        """显示带编号的国家列表"""
        display_list = ["World"] + self.countries
        
        print("\n" + "="*80)
        print("可用国家列表 (请记住编号)")
        print("="*80)
        
        # 分3列显示
        col_width = 25
        for i in range(0, len(display_list), 3):
            line = ""
            for j in range(3):
                if i + j < len(display_list):
                    item = f"{i+j+1:3d}. {display_list[i+j]}"
                    line += f"{item:<{col_width}}"
            print(line)
        
        return display_list

    def get_country_display_extent(self, country_name):
        """获取国家的显示范围"""
        if country_name == "World":
            return [-180, 180, -90, 90]
        
        # 使用预定义范围
        return self.country_extents.get(country_name, [-180, 180, -90, 90])

    def check_tif_file(self, scenario, year, month, country_name):
        """检查TIF文件是否存在"""
        # 确定使用哪个文件夹
        if any(special in country_name for special in self.special_countries):
            scenario_folder = self.scenarios[scenario]
            file_prefix = "pr"
        else:
            scenario_folder = self.scenarios_2pr[scenario]
            file_prefix = "2pr"
        
        # 构建文件路径
        tif_path = self.base_dir / scenario_folder / f"{file_prefix}_{year}{month:02d}.tif"
        
        return tif_path.exists(), str(tif_path)

    def create_demo_visualization(self, country_name, scenario, year, month, output_dir, color_scheme='viridis'):
        """创建演示可视化（不依赖复杂库）"""
        try:
            # 创建输出目录
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # 获取显示范围
            extent = self.get_country_display_extent(country_name)
            
            # 创建演示图
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # 创建演示数据
            lon_range = np.linspace(extent[0], extent[1], 100)
            lat_range = np.linspace(extent[2], extent[3], 80)
            lon_grid, lat_grid = np.meshgrid(lon_range, lat_range)
            
            # 生成演示降雨数据（乘以当月天数）
            days_in_month = calendar.monthrange(year, month)[1]
            base_rainfall = np.random.rand(80, 100) * 10  # 0-10mm/day
            monthly_rainfall = base_rainfall * days_in_month  # 转换为月总降雨量
            
            # 绘制等值线图
            im = ax.contourf(lon_grid, lat_grid, monthly_rainfall, levels=20, 
                           cmap=color_scheme, extend='max')
            
            # 添加颜色条
            cbar = plt.colorbar(im, ax=ax, shrink=0.8, pad=0.05)
            cbar.set_label('Monthly Total Precipitation (mm)', fontsize=10)
            
            # 设置地图范围
            ax.set_xlim(extent[0], extent[1])
            ax.set_ylim(extent[2], extent[3])
            
            # 添加网格线
            ax.grid(True, alpha=0.3)
            ax.set_xlabel('Longitude')
            ax.set_ylabel('Latitude')
            
            # 添加比例尺
            self.add_scale_bar(ax, extent)
            
            # 添加指北针
            self.add_north_arrow(ax, extent)
            
            # 设置标题
            title = f"{country_name} {scenario.upper()} {year}-{month:02d} Precipitation"
            ax.set_title(title, fontsize=14, pad=20)
            
            # 添加范围信息
            range_text = f"Display Range: [{extent[0]:.1f}, {extent[1]:.1f}, {extent[2]:.1f}, {extent[3]:.1f}]"
            ax.text(0.02, 0.98, range_text, transform=ax.transAxes, 
                   verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
            
            # 保存图片
            img_file = output_path / f"{country_name}_{scenario}_{year}{month:02d}.png"
            plt.savefig(img_file, dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            plt.close()
            
            # 保存CSV数据
            csv_file = output_path / f"{country_name}_{scenario}_{year}{month:02d}_data.csv"
            df = pd.DataFrame({
                'longitude': lon_grid.flatten(),
                'latitude': lat_grid.flatten(),
                'precipitation_mm': monthly_rainfall.flatten()
            })
            df.to_csv(csv_file, index=False)
            
            return True, str(img_file), str(csv_file)
            
        except Exception as e:
            print(f"可视化创建失败: {e}")
            return False, None, None

    def add_scale_bar(self, ax, extent):
        """添加比例尺"""
        try:
            # 计算比例尺位置（左下角）
            x_range = extent[1] - extent[0]
            y_range = extent[3] - extent[2]
            
            scale_x = extent[0] + x_range * 0.05
            scale_y = extent[2] + y_range * 0.1
            
            # 计算比例尺长度
            scale_length = min(x_range * 0.2, 10.0)  # 最大10度
            if scale_length > 5:
                scale_length = 5
            elif scale_length > 2:
                scale_length = 2
            elif scale_length > 1:
                scale_length = 1
            else:
                scale_length = 0.5
            
            # 绘制比例尺
            ax.plot([scale_x, scale_x + scale_length], [scale_y, scale_y], 
                   'k-', linewidth=3)
            
            # 添加标签
            ax.text(scale_x + scale_length/2, scale_y - y_range*0.03, 
                   f'{scale_length:.1f}°', ha='center', va='top', fontsize=10,
                   bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))
        except Exception as e:
            print(f"比例尺添加失败: {e}")

    def add_north_arrow(self, ax, extent):
        """添加指北针"""
        try:
            # 计算指北针位置（右上角）
            x_range = extent[1] - extent[0]
            y_range = extent[3] - extent[2]
            
            arrow_x = extent[1] - x_range * 0.08
            arrow_y = extent[3] - y_range * 0.12
            
            # 绘制箭头
            ax.annotate('', xy=(arrow_x, arrow_y + y_range*0.05), 
                       xytext=(arrow_x, arrow_y),
                       arrowprops=dict(arrowstyle='->', lw=2, color='black'))
            
            # 添加N标签
            ax.text(arrow_x, arrow_y + y_range*0.07, 'N',
                   ha='center', va='center', fontsize=12, fontweight='bold',
                   bbox=dict(boxstyle='circle,pad=0.2', facecolor='white', alpha=0.8))
        except Exception as e:
            print(f"指北针添加失败: {e}")

    def process_single_task(self, scenario, year, month, country_name, output_dir, color_scheme='viridis'):
        """处理单个任务"""
        try:
            print(f"处理: {country_name} {scenario} {year}-{month:02d}")
            
            # 检查数据文件
            file_exists, file_path = self.check_tif_file(scenario, year, month, country_name)
            if not file_exists:
                print(f"  数据文件不存在: {file_path}")
                return False, None, None
            
            print(f"  找到数据文件: {file_path}")
            
            # 创建演示可视化
            success, img_file, csv_file = self.create_demo_visualization(
                country_name, scenario, year, month, output_dir, color_scheme
            )
            
            if success:
                print(f"  ✅ 成功生成: {img_file}")
                extent = self.get_country_display_extent(country_name)
                print(f"  显示范围: {extent}")
                return True, img_file, csv_file
            else:
                print(f"  ❌ 生成失败")
                return False, None, None
                
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
            return False, None, None

def parse_selection(input_str, max_num):
    """解析用户输入的选择"""
    selections = []
    
    if input_str.lower() == 'all':
        return list(range(1, max_num + 1))
    
    # 处理逗号分隔的选择
    parts = input_str.split(',')
    
    for part in parts:
        part = part.strip()
        if '-' in part:
            # 处理范围选择，如 1-5
            try:
                start, end = map(int, part.split('-'))
                selections.extend(range(start, end + 1))
            except:
                continue
        else:
            # 处理单个选择
            try:
                num = int(part)
                if 1 <= num <= max_num:
                    selections.append(num)
            except:
                continue
    
    return sorted(list(set(selections)))

def interactive_selection():
    """交互式选择参数"""
    print("="*60)
    print("CMIP6降雨数据可视化工具 - 交互式配置")
    print("="*60)

    # 初始化工具
    print("正在初始化CMIP6工具...")
    try:
        tool = SimpleCMIP6Tool()
        print(f"✓ 初始化完成!")
    except Exception as e:
        print(f"✗ 初始化失败: {e}")
        return None

    # 1. 选择场景
    scenarios = ['ssp126', 'ssp245', 'ssp370', 'ssp585']
    print(f"\n1. 可用场景: {scenarios}")
    print("选择方式: 1,3 (选择第1和第3个) 或 all (全部) 或 1-4 (范围)")
    while True:
        scenario_input = input("请选择场景: ").strip()
        try:
            if scenario_input.lower() == 'all':
                selected_scenarios = scenarios
            else:
                indices = parse_selection(scenario_input, len(scenarios))
                selected_scenarios = [scenarios[i-1] for i in indices if 1 <= i <= len(scenarios)]

            if selected_scenarios:
                print(f"✓ 已选择场景: {selected_scenarios}")
                break
            else:
                print("无效选择，请重新输入")
        except Exception as e:
            print(f"输入错误: {e}")

    # 2. 选择年份
    years = [2025, 2026, 2027, 2028]
    print(f"\n2. 可用年份: {years}")
    print("选择方式: 2025,2027 或 all 或 2025-2027")
    while True:
        year_input = input("请选择年份: ").strip()
        try:
            if year_input.lower() == 'all':
                selected_years = years
            elif '-' in year_input:
                start, end = map(int, year_input.split('-'))
                selected_years = [y for y in range(start, end+1) if y in years]
            else:
                year_list = [int(y.strip()) for y in year_input.split(',')]
                selected_years = [y for y in year_list if y in years]

            if selected_years:
                print(f"✓ 已选择年份: {selected_years}")
                break
            else:
                print("无效选择，请重新输入")
        except Exception as e:
            print(f"输入错误: {e}")

    # 3. 选择月份
    print(f"\n3. 可用月份: 1-12")
    print("选择方式: 6,8,12 或 all 或 6-8 或 summer(6-8) 或 winter(12,1,2)")
    while True:
        month_input = input("请选择月份: ").strip()
        try:
            if month_input.lower() == 'all':
                selected_months = list(range(1, 13))
            elif month_input.lower() == 'summer':
                selected_months = [6, 7, 8]
            elif month_input.lower() == 'winter':
                selected_months = [12, 1, 2]
            elif '-' in month_input:
                start, end = map(int, month_input.split('-'))
                selected_months = list(range(start, end+1))
            else:
                selected_months = [int(m.strip()) for m in month_input.split(',')]

            # 验证月份范围
            selected_months = [m for m in selected_months if 1 <= m <= 12]

            if selected_months:
                print(f"✓ 已选择月份: {selected_months}")
                break
            else:
                print("无效选择，请重新输入")
        except Exception as e:
            print(f"输入错误: {e}")

    # 4. 选择国家
    display_list = tool.display_countries_with_numbers()
    print("选择方式: 1,3,5 (选择编号) 或 all (全部) 或 1-10 (范围)")

    while True:
        country_input = input("\n请选择国家编号: ").strip()
        try:
            if country_input.lower() == 'all':
                selected_countries = display_list
            else:
                # 解析编号输入
                indices = parse_selection(country_input, len(display_list))
                selected_countries = []

                for idx in indices:
                    if 1 <= idx <= len(display_list):
                        selected_countries.append(display_list[idx-1])

            if selected_countries:
                print(f"✓ 已选择国家: {selected_countries}")
                break
            else:
                print("无效选择，请重新输入")
        except Exception as e:
            print(f"输入错误: {e}")

    # 5. 选择颜色方案
    color_schemes = ['viridis', 'plasma', 'coolwarm', 'RdYlBu_r', 'Spectral_r', 'Blues', 'YlOrRd']
    print(f"\n5. 颜色方案: {color_schemes}")
    color_input = input("请选择颜色方案 (默认viridis): ").strip()
    color_scheme = color_input if color_input in color_schemes else 'viridis'
    print(f"✓ 已选择颜色方案: {color_scheme}")

    # 6. 输出目录
    output_dir = input("\n6. 输出目录 (默认extracted_data): ").strip()
    if not output_dir:
        output_dir = "extracted_data"
    print(f"✓ 输出目录: {output_dir}")

    # 确认配置
    total_tasks = len(selected_scenarios) * len(selected_years) * len(selected_months) * len(selected_countries)
    print(f"\n" + "="*60)
    print("配置确认:")
    print(f"  场景: {selected_scenarios}")
    print(f"  年份: {selected_years}")
    print(f"  月份: {selected_months}")
    print(f"  国家: {selected_countries}")
    print(f"  颜色: {color_scheme}")
    print(f"  输出: {output_dir}")
    print(f"  总任务数: {total_tasks}")
    print("="*60)

    confirm = input("确认开始处理? (y/n, 默认y): ").strip().lower()
    if confirm == 'n':
        print("已取消处理")
        return None

    return {
        'tool': tool,
        'scenarios': selected_scenarios,
        'years': selected_years,
        'months': selected_months,
        'countries': selected_countries,
        'color_scheme': color_scheme,
        'output_dir': output_dir
    }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='CMIP6降雨数据可视化工具 - 简化版')
    parser.add_argument('--scenarios', type=str, help='场景列表，逗号分隔 (如: ssp126,ssp245)')
    parser.add_argument('--years', type=str, help='年份列表，逗号分隔 (如: 2025,2026)')
    parser.add_argument('--months', type=str, help='月份列表，逗号分隔 (如: 6,7,8)')
    parser.add_argument('--countries', type=str, help='国家列表，逗号分隔 (如: China,Brazil)')
    parser.add_argument('--color', type=str, default='viridis', help='颜色方案')
    parser.add_argument('--output', type=str, default='extracted_data', help='输出目录')
    parser.add_argument('--interactive', action='store_true', help='交互式模式')

    args = parser.parse_args()

    if args.interactive or not any([args.scenarios, args.years, args.months, args.countries]):
        # 交互式模式
        config = interactive_selection()
        if config is None:
            return
    else:
        # 命令行参数模式
        print("使用命令行参数模式...")
        try:
            tool = SimpleCMIP6Tool()

            config = {
                'tool': tool,
                'scenarios': args.scenarios.split(',') if args.scenarios else ['ssp126'],
                'years': [int(y) for y in args.years.split(',')] if args.years else [2025],
                'months': [int(m) for m in args.months.split(',')] if args.months else [6],
                'countries': args.countries.split(',') if args.countries else ['World'],
                'color_scheme': args.color,
                'output_dir': args.output
            }
        except Exception as e:
            print(f"初始化失败: {e}")
            return

    # 执行批量处理
    print(f"\n开始批量处理...")
    try:
        tool = config['tool']
        total_tasks = len(config['scenarios']) * len(config['years']) * len(config['months']) * len(config['countries'])

        print(f"总任务数: {total_tasks}")

        success_count = 0
        failed_count = 0
        task_num = 0

        for scenario in config['scenarios']:
            for year in config['years']:
                for month in config['months']:
                    for country in config['countries']:
                        task_num += 1
                        print(f"\n[{task_num}/{total_tasks}] ", end="")

                        success, img_file, csv_file = tool.process_single_task(
                            scenario, year, month, country, config['output_dir'], config['color_scheme']
                        )

                        if success:
                            success_count += 1
                        else:
                            failed_count += 1

        print(f"\n处理完成!")
        print(f"成功: {success_count} 个任务")
        print(f"失败: {failed_count} 个任务")
        print(f"输出目录: {os.path.abspath(config['output_dir'])}")

    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
