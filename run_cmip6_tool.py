#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CMIP6降雨数据可视化工具 - 交互式运行程序
使用编号选择国家，修复地图显示问题
"""

from cmip6_rainfall_tool import CMIP6RainfallTool
import os
from pathlib import Path

def parse_selection(input_str, max_num):
    """解析用户输入的选择"""
    selections = []
    
    if input_str.lower() == 'all':
        return list(range(1, max_num + 1))
    
    # 处理逗号分隔的选择
    parts = input_str.split(',')
    
    for part in parts:
        part = part.strip()
        if '-' in part:
            # 处理范围选择，如 1-5
            try:
                start, end = map(int, part.split('-'))
                selections.extend(range(start, end + 1))
            except:
                continue
        else:
            # 处理单个选择
            try:
                num = int(part)
                if 1 <= num <= max_num:
                    selections.append(num)
            except:
                continue
    
    return sorted(list(set(selections)))

def get_user_selections():
    """获取用户选择"""
    tool = CMIP6RainfallTool()
    
    print("\n" + "="*80)
    print("🌧️  CMIP6降雨数据可视化工具")
    print("="*80)
    
    # 1. 选择场景
    print("\n1. 选择气候场景:")
    scenarios = tool.scenarios
    for i, scenario in enumerate(scenarios, 1):
        print(f"   {i}. {scenario}")
    
    while True:
        scenario_input = input("\n请输入场景编号 (1,2,3,4 或 all): ").strip()
        scenario_indices = parse_selection(scenario_input, len(scenarios))
        if scenario_indices:
            selected_scenarios = [scenarios[i-1] for i in scenario_indices]
            break
        print("输入无效，请重新输入")
    
    print(f"已选择场景: {', '.join(selected_scenarios)}")
    
    # 2. 选择年份
    print("\n2. 选择年份:")
    years = tool.years
    for i, year in enumerate(years, 1):
        print(f"   {i}. {year}")
    
    while True:
        year_input = input("\n请输入年份编号 (1,2,3,4 或 all): ").strip()
        year_indices = parse_selection(year_input, len(years))
        if year_indices:
            selected_years = [years[i-1] for i in year_indices]
            break
        print("输入无效，请重新输入")
    
    print(f"已选择年份: {', '.join(map(str, selected_years))}")
    
    # 3. 选择月份
    print("\n3. 选择月份:")
    months = tool.months
    for i in range(0, 12, 4):
        line = "   "
        for j in range(4):
            if i + j < 12:
                month_num = months[i + j]
                line += f"{month_num:2d}月({month_num:2d})  "
        print(line)
    
    while True:
        month_input = input("\n请输入月份编号 (1-12, 如: 1,3,5 或 all): ").strip()
        month_indices = parse_selection(month_input, 12)
        if month_indices:
            selected_months = month_indices
            break
        print("输入无效，请重新输入")
    
    print(f"已选择月份: {', '.join(map(str, selected_months))}")
    
    # 4. 选择国家
    print("\n4. 选择国家:")
    country_list = tool.display_countries_with_numbers()
    
    while True:
        country_input = input(f"\n请输入国家编号 (1-{len(country_list)}, 如: 1,5,10 或 all): ").strip()
        country_indices = parse_selection(country_input, len(country_list))
        if country_indices:
            selected_countries = [country_list[i-1] for i in country_indices]
            break
        print("输入无效，请重新输入")
    
    print(f"已选择国家: {', '.join(selected_countries)}")
    
    # 5. 输出目录
    output_dir = input("\n5. 输出目录 (默认: extracted_data): ").strip()
    if not output_dir:
        output_dir = "extracted_data"
    
    return selected_scenarios, selected_years, selected_months, selected_countries, output_dir

def main():
    """主函数"""
    try:
        # 获取用户选择
        scenarios, years, months, countries, output_dir = get_user_selections()
        
        # 初始化工具
        tool = CMIP6RainfallTool()
        
        # 计算总任务数
        total_tasks = len(scenarios) * len(years) * len(months) * len(countries)
        
        print(f"\n" + "="*80)
        print(f"开始处理 {total_tasks} 个任务...")
        print("="*80)
        
        # 统计结果
        success_count = 0
        failed_count = 0
        
        # 处理所有任务
        task_num = 0
        for scenario in scenarios:
            for year in years:
                for month in months:
                    for country in countries:
                        task_num += 1
                        print(f"\n[{task_num}/{total_tasks}] ", end="")
                        
                        success, img_file, csv_file = tool.process_single_task(
                            scenario, year, month, country, output_dir
                        )
                        
                        if success:
                            success_count += 1
                            print(f"✅ 成功")
                            if img_file:
                                print(f"    图片: {img_file}")
                            if csv_file:
                                print(f"    数据: {csv_file}")
                        else:
                            failed_count += 1
                            print(f"❌ 失败")
        
        # 显示结果统计
        print(f"\n" + "="*80)
        print("处理完成!")
        print(f"成功: {success_count} 个任务")
        print(f"失败: {failed_count} 个任务")
        print(f"输出目录: {os.path.abspath(output_dir)}")
        print("="*80)
        
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
    except Exception as e:
        print(f"\n程序出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
