@echo off
chcp 65001 >nul
title CMIP6降雨数据可视化工具

echo.
echo ========================================
echo 🌧️  CMIP6降雨数据可视化工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

REM 运行启动器
echo 正在启动CMIP6工具...
python start_cmip6_tool.py

if errorlevel 1 (
    echo.
    echo ❌ 程序运行出错
    echo 请检查错误信息或运行测试脚本
    echo.
    echo 💡 故障排除建议:
    echo 1. 运行 python test_cmip6_tool.py 进行系统测试
    echo 2. 检查是否安装了所需的Python库
    echo 3. 确认数据文件是否完整
    echo.
)

pause
